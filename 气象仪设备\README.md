# 气象仪设备模拟器

这个程序模拟气象仪设备，向指定串口发送标准NMEA格式的气象数据，模拟真实设备的数据变化特性。

## 功能特点

- 模拟气象仪发送的NMEA格式数据
- 支持风向、风速、温度、湿度和气压数据
- 自动计算NMEA校验和
- 高度仿真的数据变化模式：
  - 使用布朗运动模型模拟自然随机波动
  - 模拟长期趋势变化（如气压缓慢升高或降低）
  - 模拟参数间的相关性（如温度上升时湿度通常下降）
  - 数据平滑处理，避免不自然的突变
- 模拟真实设备特性：
  - 偶尔的数据延迟或丢失
  - 不精确的发送时间间隔
  - 随机变化的WIXDR发送周期
- 支持自动列出和选择可用串口
- 支持通过命令行参数自定义初始气象条件

## 数据格式

程序模拟发送两种NMEA格式数据：

1. `$WIMWV` - 风向风速数据，每秒发送一次
   - 格式：`$WIMWV,风向角度,参考系,风速,风速单位,状态*校验和`
   - 例如：`$WIMWV,265,R,1.8,M,A*36`

2. `$WIXDR` - 温度、湿度和气压数据，约每10-14秒发送一次
   - 格式：`$WIXDR,C,温度,C,0,H,湿度,P,0,P,气压,H,0*校验和`
   - 例如：`$WIXDR,C,17.0,C,0,H,74.0,P,0,P,1014.3,H,0*7C`

## 安装依赖

使用前需要安装Python串口库和numpy：

```bash
pip install -r requirements.txt
```

或者直接安装：

```bash
pip install pyserial numpy
```

## 使用方法

### 直接运行

```bash
python weather_instrument_simulator.py
```

程序会列出所有可用串口，让您选择要使用的串口。

### 指定串口

```bash
python weather_instrument_simulator.py --port COM3
```

### 指定波特率

```bash
python weather_instrument_simulator.py --port COM3 --baudrate 4800
```

### 自定义初始气象条件

```bash
python weather_instrument_simulator.py --port COM3 --temp 18.5 --humidity 65.0 --pressure 1013.2 --wind-dir 270 --wind-speed 2.5
```

## 数据参数范围

- 风向：0-360度范围内变化
- 风速：0.5-10.0 m/s范围内变化
- 温度：-20.0至50.0℃范围内变化
- 湿度：10.0-100.0%范围内变化
- 气压：950.0-1050.0 hPa范围内变化

## 数据变化模式

- **趋势变化**：每个参数都有一个持续60-180秒的趋势方向（增加或减少）
- **随机波动**：在趋势基础上叠加随机波动，模拟自然变化
- **平滑处理**：使用加权平均方法平滑数据变化，避免不自然的突变
- **参数相关性**：模拟参数间的自然相关性，如温度与湿度通常呈负相关
- **设备特性模拟**：
  - 98%的信号质量（偶尔会有数据延迟）
  - 发送时间间隔有±5%的随机波动
  - WIXDR数据发送间隔在10-14秒之间随机变化

## 注意事项

1. 确保串口未被其他程序占用
2. 默认波特率为9600，可根据实际需要调整
3. 使用Ctrl+C可随时停止模拟器 