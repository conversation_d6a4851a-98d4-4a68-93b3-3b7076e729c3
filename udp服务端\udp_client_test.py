#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UDP客户端测试工具 - 用于测试UDP服务端
可以发送文本、JSON或二进制数据
"""

import socket
import json
import time
import threading
import argparse
from datetime import datetime


class UDPClient:
    def __init__(self, server_host='localhost', server_port=8888):
        """
        初始化UDP客户端
        
        Args:
            server_host (str): 服务端地址
            server_port (int): 服务端端口
        """
        self.server_host = server_host
        self.server_port = server_port
        self.socket = None
        self.running = False
        
    def connect(self):
        """连接到UDP服务端"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            print(f"UDP客户端已创建")
            print(f"目标服务端: {self.server_host}:{self.server_port}")
            return True
        except Exception as e:
            print(f"创建UDP客户端失败: {e}")
            return False
    
    def send_text(self, message):
        """发送文本消息"""
        try:
            data = message.encode('utf-8')
            self.socket.sendto(data, (self.server_host, self.server_port))
            print(f"已发送文本: {message}")
            return True
        except Exception as e:
            print(f"发送文本失败: {e}")
            return False
    
    def send_json(self, data_dict):
        """发送JSON数据"""
        try:
            json_str = json.dumps(data_dict, ensure_ascii=False)
            data = json_str.encode('utf-8')
            self.socket.sendto(data, (self.server_host, self.server_port))
            print(f"已发送JSON: {json_str}")
            return True
        except Exception as e:
            print(f"发送JSON失败: {e}")
            return False
    
    def send_binary(self, hex_string):
        """发送二进制数据（十六进制字符串）"""
        try:
            data = bytes.fromhex(hex_string)
            self.socket.sendto(data, (self.server_host, self.server_port))
            print(f"已发送二进制数据: {hex_string}")
            return True
        except Exception as e:
            print(f"发送二进制数据失败: {e}")
            return False
    
    def send_periodic(self, message, interval=1):
        """定期发送消息"""
        self.running = True
        count = 0
        
        def send_loop():
            nonlocal count
            while self.running:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                msg = f"{message} - {count} ({timestamp})"
                self.send_text(msg)
                count += 1
                time.sleep(interval)
        
        thread = threading.Thread(target=send_loop, daemon=True)
        thread.start()
        
        print(f"开始定期发送消息，间隔: {interval}秒")
        print("按 Enter 停止...")
        input()
        self.running = False
        print("已停止定期发送")
    
    def interactive_mode(self):
        """交互模式"""
        print("\n=== UDP客户端交互模式 ===")
        print("命令:")
        print("  text <消息>     - 发送文本消息")
        print("  json <JSON>     - 发送JSON数据")
        print("  hex <十六进制>  - 发送二进制数据")
        print("  auto <消息> [间隔] - 定期发送消息")
        print("  quit/exit       - 退出")
        print()
        
        while True:
            try:
                command = input("UDP> ").strip()
                
                if not command:
                    continue
                    
                if command.lower() in ['quit', 'exit', 'q']:
                    break
                
                parts = command.split(' ', 1)
                cmd = parts[0].lower()
                
                if cmd == 'text' and len(parts) > 1:
                    self.send_text(parts[1])
                
                elif cmd == 'json' and len(parts) > 1:
                    try:
                        data = json.loads(parts[1])
                        self.send_json(data)
                    except json.JSONDecodeError as e:
                        print(f"JSON格式错误: {e}")
                
                elif cmd == 'hex' and len(parts) > 1:
                    hex_str = parts[1].replace(' ', '')
                    self.send_binary(hex_str)
                
                elif cmd == 'auto':
                    if len(parts) > 1:
                        auto_parts = parts[1].split()
                        message = auto_parts[0] if auto_parts else "测试消息"
                        interval = float(auto_parts[1]) if len(auto_parts) > 1 else 1.0
                        self.send_periodic(message, interval)
                    else:
                        self.send_periodic("测试消息")
                
                elif cmd == 'help':
                    print("可用命令: text, json, hex, auto, quit/exit")
                
                else:
                    print(f"未知命令: {cmd}，输入 help 查看帮助")
                    
            except KeyboardInterrupt:
                break
            except EOFError:
                break
    
    def close(self):
        """关闭客户端"""
        self.running = False
        if self.socket:
            self.socket.close()
        print("UDP客户端已关闭")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='UDP客户端测试工具')
    parser.add_argument('--host', default='localhost', help='服务端地址 (默认: localhost)')
    parser.add_argument('--port', type=int, default=8888, help='服务端端口 (默认: 8888)')
    parser.add_argument('--message', help='要发送的消息')
    parser.add_argument('--json', help='要发送的JSON数据')
    parser.add_argument('--hex', help='要发送的十六进制数据')
    parser.add_argument('--auto', help='自动发送消息')
    parser.add_argument('--interval', type=float, default=1.0, help='自动发送间隔 (默认: 1.0秒)')
    
    args = parser.parse_args()
    
    # 创建UDP客户端
    client = UDPClient(server_host=args.host, server_port=args.port)
    
    if not client.connect():
        return
    
    try:
        # 根据参数执行相应操作
        if args.message:
            client.send_text(args.message)
        elif args.json:
            try:
                data = json.loads(args.json)
                client.send_json(data)
            except json.JSONDecodeError as e:
                print(f"JSON格式错误: {e}")
        elif args.hex:
            client.send_binary(args.hex.replace(' ', ''))
        elif args.auto:
            client.send_periodic(args.auto, args.interval)
        else:
            # 进入交互模式
            client.interactive_mode()
            
    except KeyboardInterrupt:
        print("\n收到中断信号")
    finally:
        client.close()


if __name__ == "__main__":
    main()
