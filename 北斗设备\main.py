#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import json
import logging
import os
import sys
import time
from datetime import datetime

from nmea_generator import NMEAGenerator
from trajectory_simulator import TrajectorySimulator
from serial_communicator import SerialCommunicator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('BeidouSimulator')

def load_config(config_file='config.json'):
    """加载配置文件
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        dict: 配置信息
    """
    if not os.path.exists(config_file):
        logger.warning(f"配置文件 {config_file} 不存在，使用默认配置")
        return {
            "serial": {"port": None, "baudrate": 9600, "timeout": 1},
            "nmea": {"type": "BD", "frequency": 1},
            "trajectory": {
                "mode": "circle",
                "start_latitude": 39.9042,
                "start_longitude": 116.4074,
                "speed": 5.0,
                "circle": {"radius": 100, "clockwise": True},
                "line": {"bearing": 45},
                "file": {"path": "trajectory.json"}
            }
        }
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        logger.info(f"成功加载配置文件 {config_file}")
        return config
    except Exception as e:
        logger.error(f"加载配置文件时发生错误: {str(e)}")
        sys.exit(1)

def parse_args():
    """解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='北斗/GPS设备模拟器')
    
    # 串口参数
    parser.add_argument('--port', help='串口名称，例如COM3（Windows）或/dev/ttyUSB0（Linux）')
    parser.add_argument('--baud', type=int, help='波特率，默认9600')
    
    # NMEA参数
    parser.add_argument('--type', choices=['GNRMC', 'BDRMC'], help='NMEA数据类型，可选值：GNRMC、BDRMC')
    parser.add_argument('--freq', type=float, help='数据发送频率（Hz），默认1Hz')
    
    # 轨迹参数
    parser.add_argument('--mode', choices=['line', 'circle', 'file'], help='轨迹模式，可选值：line（直线）、circle（圆形）、file（从文件加载）')
    parser.add_argument('--file', help='当mode=file时，指定轨迹文件路径')
    parser.add_argument('--start-lat', type=float, help='起始纬度，默认为39.9042°N（北京）')
    parser.add_argument('--start-lon', type=float, help='起始经度，默认为116.4074°E（北京）')
    parser.add_argument('--speed', type=float, help='模拟速度，单位节，默认5.0')
    
    # 圆形轨迹参数
    parser.add_argument('--radius', type=float, help='圆形轨迹半径（米），默认100')
    parser.add_argument('--clockwise', action='store_true', help='圆形轨迹是否顺时针，默认顺时针')
    
    # 直线轨迹参数
    parser.add_argument('--bearing', type=float, help='直线轨迹方位角（度），默认45')
    
    # 其他参数
    parser.add_argument('--config', default='config.json', help='配置文件路径，默认为config.json')
    parser.add_argument('--list-ports', action='store_true', help='列出可用的串口')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    
    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 设置日志级别
    if args.debug:
        logger.setLevel(logging.DEBUG)
    
    # 列出可用串口
    if args.list_ports:
        ports = SerialCommunicator.list_available_ports()
        print("可用串口列表:")
        for port in ports:
            print(f"  - {port}")
        return
    
    # 加载配置
    config = load_config(args.config)
    
    # 合并命令行参数和配置文件
    serial_config = config['serial']
    nmea_config = config['nmea']
    trajectory_config = config['trajectory']
    
    # 串口参数
    port = args.port or serial_config.get('port')
    baudrate = args.baud or serial_config.get('baudrate', 9600)
    
    # NMEA参数
    nmea_type = args.type or nmea_config.get('type', 'BD')
    if nmea_type == 'BDRMC':
        nmea_type = 'BD'
    elif nmea_type == 'GNRMC':
        nmea_type = 'GN'
    frequency = args.freq or nmea_config.get('frequency', 1)
    
    # 轨迹参数
    mode = args.mode or trajectory_config.get('mode', 'circle')
    start_lat = args.start_lat or trajectory_config.get('start_latitude', 39.9042)
    start_lon = args.start_lon or trajectory_config.get('start_longitude', 116.4074)
    speed = args.speed or trajectory_config.get('speed', 5.0)
    
    # 创建轨迹模拟器
    simulator = TrajectorySimulator(start_lat=start_lat, start_lon=start_lon, speed=speed)
    
    # 创建串口通信器
    communicator = SerialCommunicator(port=port, baudrate=baudrate)
    
    # 打开串口
    if not communicator.open():
        logger.error(f"无法打开串口 {port}，请检查串口设置或使用 --list-ports 查看可用串口")
        sys.exit(1)
    
    try:
        # 轨迹参数
        if mode == 'circle':
            radius = args.radius or trajectory_config.get('circle', {}).get('radius', 100)
            clockwise = args.clockwise if args.clockwise is not None else trajectory_config.get('circle', {}).get('clockwise', True)
            logger.info(f"使用圆形轨迹模式，半径 {radius} 米，{'顺时针' if clockwise else '逆时针'}")
            
            # 计算圆心（当前位置向北移动半径的距离）
            center_lat, center_lon = simulator._calculate_new_position(start_lat, start_lon, 0, radius)
            
        elif mode == 'line':
            bearing = args.bearing or trajectory_config.get('line', {}).get('bearing', 45)
            logger.info(f"使用直线轨迹模式，方位角 {bearing} 度")
            
        elif mode == 'file':
            trajectory_file = args.file or trajectory_config.get('file', {}).get('path', 'trajectory.json')
            logger.info(f"使用文件轨迹模式，文件路径 {trajectory_file}")
            trajectory = simulator.load_trajectory_from_file(trajectory_file)
            current_index = 0
        
        # 主循环
        logger.info(f"开始发送 {nmea_type}RMC 数据，频率 {frequency} Hz")
        interval = 1.0 / frequency
        
        try:
            while True:
                # 获取当前位置
                if mode == 'circle':
                    lat, lon, course, timestamp = simulator.move_circle(
                        center_lat, center_lon, radius, clockwise, interval
                    )
                elif mode == 'line':
                    lat, lon, course, timestamp = simulator.move_line(bearing, interval)
                elif mode == 'file':
                    lat, lon, course, timestamp, current_index = simulator.move_along_trajectory(
                        trajectory, current_index, interval
                    )
                
                # 生成NMEA数据
                nmea_data = NMEAGenerator.generate_rmc(
                    lat, lon, simulator.speed, course, timestamp, nmea_type
                )
                
                # 发送数据
                if communicator.send_data(nmea_data):
                    logger.info(f"发送: {nmea_data}")
                else:
                    logger.error(f"发送失败: {nmea_data}")
                
                # 等待下一次发送
                time.sleep(interval)
                
        except KeyboardInterrupt:
            logger.info("接收到中断信号，程序退出")
        
    finally:
        # 关闭串口
        communicator.close()

if __name__ == "__main__":
    main() 