#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GPS数据发送 - 向UDP服务端发送您的GPS数据
"""

import socket
import time

def send_gps_data():
    """发送GPS数据到UDP服务端"""
    # 您的GPS数据
    hex_data = "4263316366722c3432323331363300685cfb0e00000197ab34af3d002030333242323032352d30362d32362031363a30323a34332c32312e32313138333938362c4e2c3131342e34343233353731392c45"
    
    # 转换为字节
    data_bytes = bytes.fromhex(hex_data)
    
    # 创建UDP socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    
    try:
        # 发送到UDP服务端
        server_address = ('**************', 28001)
        
        print(f"发送GPS数据到 {server_address[0]}:{server_address[1]}")
        print(f"数据长度: {len(data_bytes)} 字节")
        print(f"十六进制: {hex_data}")
        
        # 发送数据
        sock.sendto(data_bytes, server_address)
        print("数据发送成功！")
        
        # 等待一下再发送第二次（模拟连续数据）
        time.sleep(2)
        sock.sendto(data_bytes, server_address)
        print("第二次数据发送成功！")
        
    except Exception as e:
        print(f"发送失败: {e}")
    finally:
        sock.close()

if __name__ == "__main__":
    send_gps_data()
