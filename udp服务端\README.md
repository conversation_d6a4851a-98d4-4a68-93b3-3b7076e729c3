# UDP服务端

一个功能完整的UDP服务端，用于接收和处理UDP数据包。支持多客户端连接，实时显示接收数据，并提供详细的统计信息。

## 功能特性

- 🌐 支持多客户端同时连接
- 📊 实时显示接收到的数据
- 📈 详细的统计信息和客户端管理
- 🔤 自动识别文本、JSON和二进制数据
- ⚙️ 可配置监听地址、端口和缓冲区大小
- 🎮 交互式命令行界面

## 文件说明

- `udp_server.py` - UDP服务端主程序
- `udp_client_test.py` - UDP客户端测试工具
- `requirements.txt` - Python依赖包
- `start_server.bat` - Windows启动脚本
- `start_server.sh` - Linux/Mac启动脚本

## 快速开始

### 1. 启动UDP服务端

```bash
# 使用默认配置启动 (监听 0.0.0.0:8888)
python udp_server.py

# 指定监听地址和端口
python udp_server.py --host 127.0.0.1 --port 9999

# 指定缓冲区大小
python udp_server.py --buffer 2048
```

### 2. 使用测试客户端

```bash
# 交互模式
python udp_client_test.py

# 发送单条文本消息
python udp_client_test.py --message "Hello UDP Server"

# 发送JSON数据
python udp_client_test.py --json '{"type":"test","value":123}'

# 发送十六进制数据
python udp_client_test.py --hex "48656c6c6f"

# 自动发送消息 (每秒一次)
python udp_client_test.py --auto "测试消息" --interval 1.0
```

## 服务端命令

启动服务端后，可以使用以下命令：

- `stats` - 显示详细统计信息
- `clients` - 显示客户端列表
- `clear` - 清除统计信息
- `help` - 显示帮助信息
- `quit/exit` - 退出服务端

## 客户端测试命令

在客户端交互模式下，可以使用：

- `text <消息>` - 发送文本消息
- `json <JSON数据>` - 发送JSON数据
- `hex <十六进制>` - 发送二进制数据
- `auto <消息> [间隔]` - 定期发送消息
- `quit/exit` - 退出客户端

## 数据格式支持

### 文本数据
服务端会尝试将接收到的数据解码为UTF-8文本：
```
[2024-01-01 12:00:00.123] 来自 *************:12345 | 文本数据: Hello World
```

### JSON数据
如果文本数据是有效的JSON格式，会额外显示格式化的JSON：
```
[2024-01-01 12:00:00.123] 来自 *************:12345 | JSON数据: {
  "type": "sensor",
  "value": 25.6,
  "timestamp": "2024-01-01T12:00:00"
}
```

### 二进制数据
无法解码为文本的数据会显示为十六进制：
```
[2024-01-01 12:00:00.123] 来自 *************:12345 | 二进制数据 (8 字节): 48656c6c6f576f726c64
```

## 统计信息

服务端提供详细的统计信息：

- 总消息数量
- 活跃客户端数量
- 每个客户端的详细信息：
  - 首次连接时间
  - 最后活跃时间
  - 发送的消息数量

## 配置参数

### 服务端参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| --host | 0.0.0.0 | 监听地址 |
| --port | 8888 | 监听端口 |
| --buffer | 1024 | 接收缓冲区大小(字节) |

### 客户端参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| --host | localhost | 服务端地址 |
| --port | 8888 | 服务端端口 |
| --message | - | 要发送的文本消息 |
| --json | - | 要发送的JSON数据 |
| --hex | - | 要发送的十六进制数据 |
| --auto | - | 自动发送的消息 |
| --interval | 1.0 | 自动发送间隔(秒) |

## 使用场景

1. **设备数据接收** - 接收IoT设备、传感器等发送的UDP数据
2. **网络调试** - 调试UDP通信协议
3. **数据监控** - 实时监控UDP数据流
4. **协议测试** - 测试自定义UDP协议

## 注意事项

- UDP是无连接协议，不保证数据包的可靠传输
- 服务端会记录所有发送过数据的客户端信息
- 大量并发连接时，建议适当增加缓冲区大小
- 服务端支持IPv4，如需IPv6支持可修改代码

## 故障排除

### 端口被占用
```
OSError: [Errno 98] Address already in use
```
解决方案：更换端口或停止占用端口的程序

### 权限不足
```
PermissionError: [Errno 13] Permission denied
```
解决方案：使用管理员权限运行或更换端口号(>1024)

### 防火墙阻止
确保防火墙允许相应端口的UDP通信
