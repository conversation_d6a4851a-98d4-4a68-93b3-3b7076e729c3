#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
虚拟串口工具，用于在没有实际串口设备的情况下进行测试
使用com0com等虚拟串口软件创建虚拟串口对后，可以使用此脚本监听接收端
"""

import argparse
import serial
import sys
import time
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('VirtualSerial')

def list_available_ports():
    """列出可用的串口
    
    Returns:
        list: 可用串口列表
    """
    import serial.tools.list_ports
    ports = serial.tools.list_ports.comports()
    available_ports = []
    
    for port in ports:
        available_ports.append(port.device)
    
    return available_ports

def monitor_serial(port, baudrate=9600, timeout=1):
    """监听串口数据
    
    Args:
        port: 串口名称
        baudrate: 波特率，默认9600
        timeout: 超时时间，默认1秒
    """
    try:
        # 打开串口
        ser = serial.Serial(port, baudrate, timeout=timeout)
        logger.info(f"成功打开串口 {port}，波特率 {baudrate}")
        
        # 监听串口数据
        logger.info("开始监听串口数据，按Ctrl+C退出...")
        while True:
            # 读取一行数据
            line = ser.readline()
            
            # 如果有数据
            if line:
                # 解码为字符串
                try:
                    data = line.decode('utf-8').strip()
                    logger.info(f"接收: {data}")
                except UnicodeDecodeError:
                    # 如果解码失败，显示原始字节
                    logger.warning(f"接收到无法解码的数据: {line}")
            
            # 短暂延时，减少CPU使用
            time.sleep(0.01)
            
    except serial.SerialException as e:
        logger.error(f"串口错误: {str(e)}")
        return False
    except KeyboardInterrupt:
        logger.info("接收到中断信号，程序退出")
    finally:
        # 关闭串口
        if 'ser' in locals() and ser.is_open:
            ser.close()
            logger.info(f"关闭串口 {port}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='虚拟串口监听工具')
    parser.add_argument('--port', help='串口名称，例如COM3（Windows）或/dev/ttyUSB0（Linux）')
    parser.add_argument('--baud', type=int, default=9600, help='波特率，默认9600')
    parser.add_argument('--list-ports', action='store_true', help='列出可用的串口')
    
    args = parser.parse_args()
    
    # 列出可用串口
    if args.list_ports:
        ports = list_available_ports()
        print("可用串口列表:")
        for port in ports:
            print(f"  - {port}")
        return
    
    # 检查是否指定了串口
    if not args.port:
        parser.error("请指定串口，例如 --port COM3")
    
    # 监听串口数据
    monitor_serial(args.port, args.baud)

if __name__ == "__main__":
    main() 