#!/bin/bash

echo "========================================"
echo "           UDP服务端启动脚本"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误: 未找到Python，请先安装Python"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "Python版本:"
$PYTHON_CMD --version
echo

# 检查脚本文件是否存在
if [ ! -f "udp_server.py" ]; then
    echo "错误: 未找到 udp_server.py 文件"
    exit 1
fi

echo "启动UDP服务端..."
echo "默认监听地址: 0.0.0.0:8888"
echo "按 Ctrl+C 停止服务端"
echo
echo "========================================"
echo

# 启动UDP服务端
$PYTHON_CMD udp_server.py

echo
echo "UDP服务端已停止"
