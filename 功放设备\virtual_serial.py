#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
虚拟串口工具
用于创建虚拟串口对并启动BUC功放设备模拟器和测试客户端
"""

import argparse
import logging
import os
import platform
import subprocess
import sys
import threading
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('VirtualSerial')

def is_admin():
    """检查是否具有管理员权限"""
    try:
        if platform.system() == 'Windows':
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        else:
            return os.geteuid() == 0
    except:
        return False

def create_virtual_serial_pair():
    """
    创建虚拟串口对
    
    Returns:
        tuple or None: 成功时返回 (port1, port2)，失败时返回 None
    """
    system = platform.system()
    
    if system == 'Windows':
        try:
            # 检查是否已安装 com0com
            import winreg
            winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r'SOFTWARE\com0com')
            logger.info("检测到com0com已安装")
            
            # 尝试使用 setupc 创建或配置虚拟串口对
            if not is_admin():
                logger.warning("需要管理员权限来配置虚拟串口。请以管理员身份运行该脚本。")
                return None
            
            # 查找可用的端口号
            from serial.tools.list_ports import comports
            existing_ports = [port.device for port in comports()]
            
            # 找到两个尚未使用的 COM 端口
            available_com = []
            for i in range(1, 256):
                port_name = f"COM{i}"
                if port_name not in existing_ports and len(available_com) < 2:
                    available_com.append(port_name)
                if len(available_com) == 2:
                    break
            
            if len(available_com) < 2:
                logger.error("找不到足够的可用COM端口")
                return None
            
            port1, port2 = available_com
            
            # 配置 com0com
            setupc_cmd = [
                r"C:\Program Files (x86)\com0com\setupc.exe",
                "install", f"PortName={port1}", f"PortName={port2}"
            ]
            
            logger.info(f"正在创建虚拟串口对: {port1} <-> {port2}")
            subprocess.run(setupc_cmd, shell=True, check=True)
            
            # 等待一会儿，让操作系统识别新端口
            time.sleep(2)
            
            logger.info(f"虚拟串口对创建成功: {port1} <-> {port2}")
            return port1, port2
            
        except Exception as e:
            logger.error(f"创建虚拟串口对失败: {e}")
            logger.info("提示: 在Windows上，你需要安装com0com工具并以管理员身份运行该脚本")
            logger.info("你可以从这里下载com0com: https://sourceforge.net/projects/com0com/")
            return None
            
    elif system == 'Linux':
        try:
            # 使用 socat 创建虚拟串口对
            # 检查 socat 是否已安装
            try:
                subprocess.run(["socat", "-V"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
            except:
                logger.error("未找到socat。请安装socat工具: sudo apt-get install socat")
                return None
            
            # 创建两个 PTY 设备作为虚拟串口
            process = subprocess.Popen(
                ["socat", "-d", "-d", "pty,raw,echo=0", "pty,raw,echo=0"],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
            )
            
            # 从输出中提取设备名称
            time.sleep(0.5)
            stderr_output = process.stderr.readline() + process.stderr.readline()
            
            import re
            devices = re.findall(r'/dev/pts/\d+', stderr_output)
            
            if len(devices) >= 2:
                port1, port2 = devices[0], devices[1]
                logger.info(f"虚拟串口对创建成功: {port1} <-> {port2}")
                
                # 保存 socat 进程以便以后清理
                global socat_process
                socat_process = process
                
                return port1, port2
            else:
                logger.error("无法创建虚拟串口对")
                if process:
                    process.terminate()
                return None
                
        except Exception as e:
            logger.error(f"创建虚拟串口对失败: {e}")
            return None
    
    else:  # macOS 或其他系统
        logger.error(f"不支持的操作系统: {system}")
        logger.info("请手动安装合适的虚拟串口工具，然后使用 -p 参数指定串口")
        return None

def manual_input_ports():
    """手动输入串口对"""
    print("\n请手动输入两个串口名称:")
    port1 = input("模拟器使用的串口 (例如 COM1): ")
    port2 = input("客户端使用的串口 (例如 COM2): ")
    
    if not port1 or not port2:
        print("串口名称不能为空")
        return None
    
    return port1, port2

def start_simulator_and_client(port1, port2):
    """
    启动BUC功放设备模拟器和测试客户端
    
    Args:
        port1 (str): 模拟器使用的串口
        port2 (str): 客户端使用的串口
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 定义模拟器和客户端启动命令
    simulator_cmd = [sys.executable, os.path.join(current_dir, "buc_simulator.py")]
    client_cmd = [sys.executable, os.path.join(current_dir, "buc_client.py")]
    
    # 定义一个函数来运行命令并处理输出
    def run_command(cmd, name):
        logger.info(f"启动{name}: {' '.join(cmd)}")
        try:
            process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                text=True, bufsize=1, stdin=subprocess.PIPE
            )
            
            # 记录命令进程以便以后清理
            if name == "模拟器":
                global simulator_process
                simulator_process = process
                
                # 为模拟器提供输入
                if process.stdin:
                    # 等待一会儿，让模拟器准备好接收输入
                    time.sleep(1)
                    process.stdin.write(f"{port1}\n")
                    process.stdin.write("\n")  # 默认波特率
                    process.stdin.write("n\n") # 不显示详细日志
                    process.stdin.flush()
            else:
                global client_process
                client_process = process
                
                # 为客户端提供输入
                if process.stdin:
                    # 等待一会儿，让客户端准备好接收输入
                    time.sleep(1)
                    process.stdin.write(f"{port2}\n")
                    process.stdin.write("\n")  # 默认波特率
                    process.stdin.write("n\n") # 不显示详细日志
                    process.stdin.flush()
            
            # 读取并打印输出
            prefix = f"[{name}] "
            while True:
                line = process.stdout.readline()
                if not line and process.poll() is not None:
                    break
                if line:
                    print(f"{prefix}{line.rstrip()}")
            
            return_code = process.poll()
            logger.info(f"{name}进程已退出，返回码: {return_code}")
            
        except Exception as e:
            logger.error(f"{name}启动失败: {e}")
    
    # 创建并启动模拟器线程
    simulator_thread = threading.Thread(target=run_command, args=(simulator_cmd, "模拟器"))
    simulator_thread.daemon = True
    simulator_thread.start()
    
    # 等待几秒钟，让模拟器先启动
    time.sleep(3)
    
    # 创建并启动客户端线程
    client_thread = threading.Thread(target=run_command, args=(client_cmd, "客户端"))
    client_thread.daemon = True
    client_thread.start()
    
    # 主线程等待用户中断
    try:
        print("\n模拟器和客户端已启动。按Ctrl+C可停止所有程序。")
        while simulator_thread.is_alive() or client_thread.is_alive():
            time.sleep(0.1)
    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在停止...")
        
        # 尝试终止进程
        if 'simulator_process' in globals() and simulator_process:
            simulator_process.terminate()
        if 'client_process' in globals() and client_process:
            client_process.terminate()
        if 'socat_process' in globals() and socat_process:
            socat_process.terminate()


def main():
    """主函数"""
    print("="*50)
    print("BUC功放设备虚拟串口工具")
    print("用于创建虚拟串口对并启动模拟器和客户端")
    print("="*50)
    
    # 询问用户是否需要自动创建虚拟串口对
    auto_create = input("\n是否自动创建虚拟串口对? (y/n) [y]: ").lower() != 'n'
    
    # 获取串口对
    if auto_create:
        print("\n正在尝试自动创建虚拟串口对...")
        ports = create_virtual_serial_pair()
        if not ports:
            print("\n自动创建虚拟串口对失败。")
            manual_choice = input("是否手动指定串口? (y/n) [y]: ").lower() != 'n'
            if manual_choice:
                ports = manual_input_ports()
            else:
                print("无法继续，程序将退出。")
                return
    else:
        ports = manual_input_ports()
    
    # 检查是否成功获取串口对
    if not ports:
        print("未指定有效的串口对，程序将退出。")
        return
    
    port1, port2 = ports
    print(f"\n将使用以下串口对:\n模拟器: {port1}\n客户端: {port2}")
    confirm = input("\n确认启动模拟器和客户端? (y/n) [y]: ").lower() != 'n'
    
    if confirm:
        # 启动模拟器和客户端
        start_simulator_and_client(port1, port2)
    else:
        print("已取消启动，程序将退出。")


if __name__ == '__main__':
    # 初始化全局进程变量
    simulator_process = None
    client_process = None
    socat_process = None
    
    try:
        main()
    except Exception as e:
        logger.error(f"程序执行过程中发生错误: {e}")
    finally:
        # 确保所有进程都被终止
        if 'simulator_process' in globals() and simulator_process:
            simulator_process.terminate()
        if 'client_process' in globals() and client_process:
            client_process.terminate()
        if 'socat_process' in globals() and socat_process:
            socat_process.terminate() 