#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS数据解析器 - 专门解析包含GPS信息的UDP数据包
"""

import struct
import re
from datetime import datetime


class GPSDataParser:
    def __init__(self):
        pass
    
    def parse_gps_data(self, hex_string):
        """解析GPS数据包"""
        print(f"原始数据: {hex_string}")
        print("-" * 80)
        
        try:
            # 转换为字节
            data_bytes = bytes.fromhex(hex_string)
            
            # 解析数据结构
            self._parse_structure(data_bytes)
            
        except Exception as e:
            print(f"解析错误: {e}")
    
    def _parse_structure(self, data_bytes):
        """解析数据结构"""
        print("=== 数据结构分析 ===")
        
        # 显示十六进制和ASCII对照
        self._show_hex_ascii(data_bytes)
        
        # 提取GPS信息
        self._extract_gps_info(data_bytes)
    
    def _show_hex_ascii(self, data_bytes):
        """显示十六进制和ASCII对照"""
        print("\n十六进制 <-> ASCII 对照:")
        for i in range(0, len(data_bytes), 16):
            chunk = data_bytes[i:i+16]
            hex_part = ' '.join(f'{b:02x}' for b in chunk)
            ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
            print(f"{i:04x}: {hex_part:<48} |{ascii_part}|")
    
    def _extract_gps_info(self, data_bytes):
        """提取GPS信息"""
        print("\n=== GPS信息提取 ===")
        
        try:
            # 尝试解码为ASCII文本
            text_data = data_bytes.decode('ascii', errors='ignore')
            print(f"文本内容: {repr(text_data)}")
            
            # 分析数据段
            self._analyze_segments(data_bytes, text_data)
            
        except Exception as e:
            print(f"GPS信息提取失败: {e}")
    
    def _analyze_segments(self, data_bytes, text_data):
        """分析数据段"""
        print("\n--- 数据段分析 ---")
        
        # 第一段：设备标识和ID
        segment1 = data_bytes[0:15]  # Bc1cfr,4223163
        device_info = segment1.decode('ascii', errors='ignore').rstrip('\x00')
        print(f"设备信息: {device_info}")
        
        if ',' in device_info:
            parts = device_info.split(',')
            print(f"  设备类型/标识: {parts[0]}")
            print(f"  设备ID: {parts[1]}")
        
        # 中间段：二进制数据（可能是时间戳或其他控制信息）
        binary_segment = data_bytes[15:28]
        print(f"\n二进制段 (13字节): {binary_segment.hex()}")
        
        # 尝试解析时间戳
        self._try_parse_timestamp(binary_segment)
        
        # GPS数据段
        gps_start = 28
        gps_data = data_bytes[gps_start:].decode('ascii', errors='ignore')
        print(f"\nGPS数据段: {gps_data}")
        
        # 解析GPS字段
        self._parse_gps_fields(gps_data)
    
    def _try_parse_timestamp(self, binary_data):
        """尝试解析时间戳"""
        print("  尝试解析时间戳:")
        
        # 尝试不同的时间戳格式
        if len(binary_data) >= 4:
            # Unix时间戳 (4字节)
            try:
                timestamp = struct.unpack('<I', binary_data[:4])[0]
                dt = datetime.fromtimestamp(timestamp)
                print(f"    Unix时间戳(小端): {timestamp} -> {dt}")
            except:
                pass
            
            try:
                timestamp = struct.unpack('>I', binary_data[:4])[0]
                dt = datetime.fromtimestamp(timestamp)
                print(f"    Unix时间戳(大端): {timestamp} -> {dt}")
            except:
                pass
        
        if len(binary_data) >= 8:
            # 64位时间戳
            try:
                timestamp = struct.unpack('<Q', binary_data[:8])[0]
                if timestamp < 2**32:  # 合理的时间戳范围
                    dt = datetime.fromtimestamp(timestamp)
                    print(f"    64位时间戳(小端): {timestamp} -> {dt}")
            except:
                pass
    
    def _parse_gps_fields(self, gps_data):
        """解析GPS字段"""
        print("\n--- GPS字段解析 ---")
        
        # 使用正则表达式提取GPS信息
        # 格式似乎是: 032B2025-06-26 16:02:43,21.21183986,N,114.44235719,E
        
        # 提取日期时间
        datetime_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})'
        datetime_match = re.search(datetime_pattern, gps_data)
        if datetime_match:
            gps_datetime = datetime_match.group(1)
            print(f"GPS时间: {gps_datetime}")
        
        # 提取坐标信息
        # 纬度格式: 度.分, N/S
        lat_pattern = r'(\d+\.\d+),([NS])'
        lat_match = re.search(lat_pattern, gps_data)
        if lat_match:
            lat_value = float(lat_match.group(1))
            lat_dir = lat_match.group(2)
            
            # 转换为十进制度数
            lat_degrees = self._convert_to_decimal_degrees(lat_value)
            if lat_dir == 'S':
                lat_degrees = -lat_degrees
            
            print(f"纬度: {lat_value}° {lat_dir} = {lat_degrees:.8f}°")
        
        # 经度格式: 度.分, E/W
        lon_pattern = r'(\d+\.\d+),([EW])'
        lon_match = re.search(lon_pattern, gps_data)
        if lon_match:
            lon_value = float(lon_match.group(1))
            lon_dir = lon_match.group(2)
            
            # 转换为十进制度数
            lon_degrees = self._convert_to_decimal_degrees(lon_value)
            if lon_dir == 'W':
                lon_degrees = -lon_degrees
            
            print(f"经度: {lon_value}° {lon_dir} = {lon_degrees:.8f}°")
        
        # 显示地图链接
        if lat_match and lon_match:
            lat_decimal = self._convert_to_decimal_degrees(float(lat_match.group(1)))
            if lat_match.group(2) == 'S':
                lat_decimal = -lat_decimal
            
            lon_decimal = self._convert_to_decimal_degrees(float(lon_match.group(1)))
            if lon_match.group(2) == 'W':
                lon_decimal = -lon_decimal
            
            print(f"\n地图位置:")
            print(f"  Google Maps: https://maps.google.com/?q={lat_decimal},{lon_decimal}")
            print(f"  百度地图: https://map.baidu.com/?q={lat_decimal},{lon_decimal}")
    
    def _convert_to_decimal_degrees(self, coord_value):
        """将度分格式转换为十进制度数"""
        # 假设格式是 DDMM.MMMM (度分.分的小数部分)
        degrees = int(coord_value // 100)
        minutes = coord_value % 100
        decimal_degrees = degrees + minutes / 60.0
        return decimal_degrees


def main():
    """主函数"""
    parser = GPSDataParser()
    
    # 您的数据
    sample_data = "4263316366722c3432323331363300685cfb0e00000197ab34af3d002030333242323032352d30362d32362031363a30323a34332c32312e32313138333938362c4e2c3131342e34343233353731392c45"
    
    print("GPS数据解析器")
    print("=" * 80)
    
    parser.parse_gps_data(sample_data)
    
    print("\n" + "=" * 80)
    print("解析完成")


if __name__ == "__main__":
    main()
