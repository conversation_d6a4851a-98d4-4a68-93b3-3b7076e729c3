#!/usr/bin/env python
# -*- coding: utf-8 -*-

import datetime
import math

class NMEAGenerator:
    """NMEA-0183协议数据生成器"""
    
    @staticmethod
    def calculate_checksum(sentence):
        """计算NMEA校验和
        
        Args:
            sentence: NMEA语句，不包含起始的$和结尾的*校验和
            
        Returns:
            两位十六进制校验和字符串
        """
        # 校验和是所有字符的异或值
        checksum = 0
        for char in sentence:
            checksum ^= ord(char)
        # 转换为两位十六进制
        return f"{checksum:02X}"
    
    @staticmethod
    def format_latitude(latitude):
        """将十进制纬度格式化为NMEA格式 (ddmm.mmmmm)
        
        Args:
            latitude: 十进制纬度，正值表示北纬，负值表示南纬
            
        Returns:
            tuple: (ddmm.mmmmm格式的纬度字符串, 'N'或'S'指示符)
        """
        hemisphere = 'N' if latitude >= 0 else 'S'
        latitude = abs(latitude)
        degrees = int(latitude)
        minutes = (latitude - degrees) * 60
        # NMEA格式：ddmm.mmmmm
        return f"{degrees:02d}{minutes:08.5f}", hemisphere
    
    @staticmethod
    def format_longitude(longitude):
        """将十进制经度格式化为NMEA格式 (dddmm.mmmmm)
        
        Args:
            longitude: 十进制经度，正值表示东经，负值表示西经
            
        Returns:
            tuple: (dddmm.mmmmm格式的经度字符串, 'E'或'W'指示符)
        """
        hemisphere = 'E' if longitude >= 0 else 'W'
        longitude = abs(longitude)
        degrees = int(longitude)
        minutes = (longitude - degrees) * 60
        # NMEA格式：dddmm.mmmmm
        return f"{degrees:03d}{minutes:08.5f}", hemisphere
    
    @staticmethod
    def generate_rmc(latitude, longitude, speed=0.0, course=0.0, 
                     timestamp=None, nmea_type="BD"):
        """生成RMC语句
        
        Args:
            latitude: 十进制纬度
            longitude: 十进制经度
            speed: 速度，单位节
            course: 航向，单位度
            timestamp: 时间戳，如果为None则使用当前时间
            nmea_type: NMEA类型，"BD"表示北斗，"GN"表示GNSS
            
        Returns:
            完整的NMEA RMC语句
        """
        if timestamp is None:
            timestamp = datetime.datetime.utcnow()
        
        # 格式化时间 HHMMSS.SS
        time_str = timestamp.strftime("%H%M%S.00")
        
        # 格式化日期 DDMMYY
        date_str = timestamp.strftime("%d%m%y")
        
        # 格式化位置
        lat_str, lat_dir = NMEAGenerator.format_latitude(latitude)
        lon_str, lon_dir = NMEAGenerator.format_longitude(longitude)
        
        # 格式化速度和航向
        speed_str = f"{speed:.3f}"
        course_str = f"{course:.2f}"
        
        # 构建RMC语句，不包含起始的$和结尾的*校验和
        rmc_type = f"{nmea_type}RMC"
        rmc_body = f"{rmc_type},{time_str},A,{lat_str},{lat_dir},{lon_str},{lon_dir},{speed_str},{course_str},{date_str},,,A"
        
        # 计算校验和
        checksum = NMEAGenerator.calculate_checksum(rmc_body)
        
        # 返回完整的NMEA语句
        return f"${rmc_body}*{checksum}" 