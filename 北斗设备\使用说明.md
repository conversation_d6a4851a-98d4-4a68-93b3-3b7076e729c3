# 北斗设备模拟器使用说明

本模拟器用于模拟北斗/GPS设备发送NMEA-0183协议格式的定位数据，支持多种轨迹模式和配置选项。

## 功能特点

- 支持生成标准NMEA-0183协议格式的GNRMC/BDRMC数据
- 可通过串口发送模拟数据
- 支持多种轨迹模式：直线、圆形和自定义轨迹
- 可配置发送频率、起始位置等参数

## 安装依赖

在使用模拟器之前，需要安装Python 3.6+和必要的依赖库：

```bash
# Windows
pip install -r requirements.txt

# Linux/macOS
pip3 install -r requirements.txt
```

## 快速启动

### Windows用户

双击运行`start.bat`文件，按照提示选择操作模式并输入相关参数。

### Linux/macOS用户

首先添加执行权限：

```bash
chmod +x start.sh
```

然后运行脚本：

```bash
./start.sh
```

按照提示选择操作模式并输入相关参数。

## 使用方法

### 命令行参数

也可以直接使用命令行参数运行模拟器：

```bash
# Windows
python main.py --port COM3 --baud 9600 --freq 1 --mode circle

# Linux/macOS
python3 main.py --port /dev/ttyUSB0 --baud 9600 --freq 1 --mode circle
```

### 参数说明

- `--port`: 串口名称，例如COM3（Windows）或/dev/ttyUSB0（Linux）
- `--baud`: 波特率，默认9600
- `--freq`: 数据发送频率（Hz），默认1Hz
- `--mode`: 轨迹模式，可选值：line（直线）、circle（圆形）、file（从文件加载）
- `--file`: 当mode=file时，指定轨迹文件路径
- `--start-lat`: 起始纬度，默认为39.9042°N（北京）
- `--start-lon`: 起始经度，默认为116.4074°E（北京）
- `--speed`: 模拟速度，单位节，默认5.0
- `--radius`: 圆形轨迹半径（米），默认100
- `--clockwise`: 圆形轨迹是否顺时针，默认顺时针
- `--bearing`: 直线轨迹方位角（度），默认45
- `--type`: NMEA数据类型，可选值：GNRMC、BDRMC，默认BDRMC
- `--config`: 配置文件路径，默认为config.json
- `--list-ports`: 列出可用的串口
- `--debug`: 启用调试模式

## 配置文件

可以通过修改`config.json`文件来设置默认参数：

```json
{
    "serial": {
        "port": "COM3",
        "baudrate": 9600,
        "timeout": 1
    },
    "nmea": {
        "type": "BD",
        "frequency": 1
    },
    "trajectory": {
        "mode": "circle",
        "start_latitude": 39.9042,
        "start_longitude": 116.4074,
        "speed": 5.0,
        "circle": {
            "radius": 100,
            "clockwise": true
        },
        "line": {
            "bearing": 45
        },
        "file": {
            "path": "trajectory.json"
        }
    }
}
```

## 自定义轨迹

可以通过创建JSON格式的轨迹文件来定义自定义轨迹，文件格式如下：

```json
[
    {"lat": 39.9042, "lon": 116.4074},
    {"lat": 39.9052, "lon": 116.4084},
    {"lat": 39.9062, "lon": 116.4094},
    ...
]
```

然后使用`--mode file --file 轨迹文件路径`参数运行模拟器。

## 测试模式

如果没有实际串口设备，可以使用测试模式查看生成的NMEA数据：

```bash
# Windows
python test_nmea.py

# Linux/macOS
python3 test_nmea.py
```

## 虚拟串口测试

在没有实际串口设备的情况下，可以使用虚拟串口软件（如com0com、socat等）创建虚拟串口对，然后使用`virtual_serial.py`脚本监听接收端：

```bash
# Windows
python virtual_serial.py --port COM4

# Linux/macOS
python3 virtual_serial.py --port /dev/ttyS1
```

## 常见问题

1. **找不到串口**
   - 使用`--list-ports`参数查看可用串口
   - 检查设备连接是否正常
   - 检查是否安装了正确的驱动程序

2. **无法打开串口**
   - 检查串口是否被其他程序占用
   - 尝试使用管理员/root权限运行程序

3. **数据发送失败**
   - 检查串口配置（波特率等）是否正确
   - 检查接收端是否正常工作

## 支持的NMEA语句

目前支持以下NMEA语句：

- `$GNRMC`: GNSS推荐最小定位信息
- `$BDRMC`: 北斗系统推荐最小定位信息

NMEA-0183协议RMC语句格式：
```
$XXRMC,时间,状态,纬度,N/S,经度,E/W,速度,方向,日期,磁偏角,E/W*校验和
```

示例：
```
$GNRMC,083559.00,A,4717.11437,N,00833.91522,E,0.004,77.52,091202,,,A*57
$BDRMC,083559.00,A,3954.49642,N,11624.42396,E,5.000,45.00,091202,,,A*59
``` 