@echo off
echo 北斗设备模拟器启动脚本
echo ============================

REM 检查Python环境
where python >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误：未找到Python环境，请安装Python 3.6+
    pause
    exit /b 1
)

REM 检查依赖
echo 正在检查依赖...
pip install -r requirements.txt

REM 列出可用串口
echo.
echo 可用串口列表:
python -c "import serial.tools.list_ports; ports = serial.tools.list_ports.comports(); print('\n'.join([f'  - {p.device}' for p in ports]))"

echo.
echo 请选择操作模式:
echo 1. 直线轨迹模式
echo 2. 圆形轨迹模式
echo 3. 文件轨迹模式
echo 4. 测试模式（不使用串口）
echo 5. 退出

set /p mode=请输入选项(1-5): 

if "%mode%"=="1" (
    set /p port=请输入串口名称(例如COM3): 
    set /p bearing=请输入方位角(0-360度，默认45): 
    if "%bearing%"=="" set bearing=45
    python main.py --port %port% --mode line --bearing %bearing%
) else if "%mode%"=="2" (
    set /p port=请输入串口名称(例如COM3): 
    set /p radius=请输入圆形轨迹半径(米，默认100): 
    if "%radius%"=="" set radius=100
    python main.py --port %port% --mode circle --radius %radius%
) else if "%mode%"=="3" (
    set /p port=请输入串口名称(例如COM3): 
    set /p file=请输入轨迹文件路径(默认trajectory.json): 
    if "%file%"=="" set file=trajectory.json
    python main.py --port %port% --mode file --file %file%
) else if "%mode%"=="4" (
    python test_nmea.py
) else if "%mode%"=="5" (
    echo 退出程序
    exit /b 0
) else (
    echo 无效选项，请重新运行脚本
    pause
    exit /b 1
)

pause 