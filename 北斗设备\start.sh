#!/bin/bash

echo "北斗设备模拟器启动脚本"
echo "============================"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误：未找到Python环境，请安装Python 3.6+"
    exit 1
fi

# 检查依赖
echo "正在检查依赖..."
pip3 install -r requirements.txt

# 列出可用串口
echo
echo "可用串口列表:"
python3 -c "import serial.tools.list_ports; ports = serial.tools.list_ports.comports(); print('\n'.join([f'  - {p.device}' for p in ports]))"

echo
echo "请选择操作模式:"
echo "1. 直线轨迹模式"
echo "2. 圆形轨迹模式"
echo "3. 文件轨迹模式"
echo "4. 测试模式（不使用串口）"
echo "5. 退出"

read -p "请输入选项(1-5): " mode

case $mode in
    1)
        read -p "请输入串口名称(例如/dev/ttyUSB0): " port
        read -p "请输入方位角(0-360度，默认45): " bearing
        bearing=${bearing:-45}
        python3 main.py --port "$port" --mode line --bearing "$bearing"
        ;;
    2)
        read -p "请输入串口名称(例如/dev/ttyUSB0): " port
        read -p "请输入圆形轨迹半径(米，默认100): " radius
        radius=${radius:-100}
        python3 main.py --port "$port" --mode circle --radius "$radius"
        ;;
    3)
        read -p "请输入串口名称(例如/dev/ttyUSB0): " port
        read -p "请输入轨迹文件路径(默认trajectory.json): " file
        file=${file:-trajectory.json}
        python3 main.py --port "$port" --mode file --file "$file"
        ;;
    4)
        python3 test_nmea.py
        ;;
    5)
        echo "退出程序"
        exit 0
        ;;
    *)
        echo "无效选项，请重新运行脚本"
        exit 1
        ;;
esac 