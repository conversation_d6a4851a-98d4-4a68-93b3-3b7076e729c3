# BUC功放设备模拟器

根据L-BUC设备软件指令集实现的串口设备模拟器，用于模拟功放设备进行调试。

## 功能特点

- 完全模拟BUC功放设备的串口通信协议
- 支持设备查询、BUC单独查询、变量设置、功放开关设置等功能
- 提供实时状态更新，包括温度、功率等参数
- 同时提供测试客户端用于验证通信

## 环境要求

- Python 3.6+
- pyserial库

## 安装依赖

```bash
pip install pyserial
```

## 使用方法

### 1. 使用虚拟串口进行测试（无需实际串口设备）

最简单的方法是使用虚拟串口工具，它可以在单机环境下模拟两个串口之间的通信：

```bash
python virtual_serial.py
```

这将自动创建两个虚拟串口，并使用它们启动模拟器和客户端进行通信测试。

参数说明：
- `-p1, --port1`: 模拟器使用的串口名称，默认COM_SIM
- `-p2, --port2`: 客户端使用的串口名称，默认COM_CLI

### 2. 单独启动模拟器

如果您有实际的串口设备，或使用第三方虚拟串口软件，可以单独启动模拟器：

```bash
python buc_simulator.py -p COM1  # Windows
# 或
python buc_simulator.py -p /dev/ttyUSB0  # Linux/Mac
```

参数说明：
- `-p, --port`: 串口名称，必需参数
- `-b, --baudrate`: 波特率，默认9600
- `-v, --verbose`: 显示详细日志

### 3. 单独使用测试客户端

同样，您可以单独启动测试客户端：

```bash
python buc_client.py -p COM2  # Windows，使用另一个串口
# 或
python buc_client.py -p /dev/ttyUSB1  # Linux/Mac，使用另一个串口
```

参数说明：
- `-p, --port`: 串口名称，必需参数
- `-b, --baudrate`: 波特率，默认9600
- `-v, --verbose`: 显示详细日志
- `-c, --command`: 要执行的命令，可选值：
  - `query`: 执行设备查询命令
  - `buc`: 执行BUC单独查询命令
  - `var`: 执行变量设置命令
  - `power_on`: 执行功放开启命令
  - `power_off`: 执行功放关闭命令
  - `all`: 执行所有命令（默认）

## 模拟器实现的指令

### 1. 设备查询
- 发送命令：`16 01 31 31`
- 响应：`11 03 31 AA FB`

### 2. BUC单独查询
- 发送命令：`16 02 32 A2 90`
- 响应：`11 09 32 55 YY YY YY YY YY YY`（YY为随机数据）

### 3. 变量设置
- 发送命令：`16 03 33 A3 55 75`
- 响应：`11 04 A3 55 XX YY`（XX和YY为随机数据）

### 4. 功放开关设置
- 开启命令：`16 02 34 A4 90`
- 开启响应：`11 04 54 55 55 54`
- 关闭命令：`16 02 34 A5 91`
- 关闭响应：`11 04 54 AA AB`

## 工作原理

模拟器启动后会创建两个线程：
1. 主线程：处理用户输入和程序控制
2. 串口监听线程：监听串口命令并进行响应
3. 状态更新线程：定期更新设备状态

测试客户端则提供了一个简单的接口，可以向模拟器发送各种命令并显示响应结果。

## 调试提示

1. 在Windows下，可以使用com0com等虚拟串口软件创建一对虚拟串口进行测试
2. 在Linux下，可以使用socat创建虚拟串口对：
   ```bash
   socat -d -d pty,raw,echo=0 pty,raw,echo=0
   ```
3. 使用串口调试工具（如Serial Port Monitor）可以监控通信过程

## 注意事项

- 确保使用的串口没有被其他程序占用
- 确保有足够的权限访问串口设备
- CRC校验算法为简化版本，实际应用中应根据具体协议实现

## 协议说明

根据L-BUC设备软件指令集，通信协议如下：
- 串口参数：波特率9600，无校验位，数据位8，停止位1
- 命令格式：帧头(1字节) + 帧长(1字节) + 数据(n字节) + CRC校验(1字节)
- 帧头固定为0x16
- 帧长为数据部分的长度
- CRC校验为数据部分所有字节的异或值 