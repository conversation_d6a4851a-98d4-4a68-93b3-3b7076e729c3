#!/usr/bin/env python
# -*- coding: utf-8 -*-

import serial
import time
import logging

class SerialCommunicator:
    """串口通信模块，用于发送NMEA数据到串口"""
    
    def __init__(self, port=None, baudrate=9600, timeout=1):
        """初始化串口通信
        
        Args:
            port: 串口名称，例如COM3（Windows）或/dev/ttyUSB0（Linux）
            baudrate: 波特率，默认9600
            timeout: 超时时间，默认1秒
        """
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.serial_port = None
        self.logger = logging.getLogger('SerialCommunicator')
    
    def open(self):
        """打开串口连接
        
        Returns:
            bool: 是否成功打开
        """
        if self.port is None:
            self.logger.error("未指定串口")
            return False
        
        try:
            self.serial_port = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout
            )
            
            if self.serial_port.is_open:
                self.logger.info(f"成功打开串口 {self.port}，波特率 {self.baudrate}")
                return True
            else:
                self.logger.error(f"无法打开串口 {self.port}")
                return False
                
        except serial.SerialException as e:
            self.logger.error(f"打开串口时发生错误: {str(e)}")
            return False
    
    def close(self):
        """关闭串口连接"""
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
            self.logger.info(f"关闭串口 {self.port}")
    
    def send_data(self, data):
        """发送数据到串口
        
        Args:
            data: 要发送的数据字符串
            
        Returns:
            bool: 是否成功发送
        """
        if not self.serial_port or not self.serial_port.is_open:
            self.logger.error("串口未打开")
            return False
        
        try:
            # 确保数据以换行符结尾
            if not data.endswith('\r\n'):
                data += '\r\n'
            
            # 将字符串转换为字节
            data_bytes = data.encode('utf-8')
            
            # 发送数据
            bytes_written = self.serial_port.write(data_bytes)
            self.logger.debug(f"发送数据: {data.strip()}")
            
            return bytes_written == len(data_bytes)
            
        except Exception as e:
            self.logger.error(f"发送数据时发生错误: {str(e)}")
            return False
    
    def list_available_ports():
        """列出可用的串口
        
        Returns:
            list: 可用串口列表
        """
        import serial.tools.list_ports
        ports = serial.tools.list_ports.comports()
        available_ports = []
        
        for port in ports:
            available_ports.append(port.device)
        
        return available_ports 