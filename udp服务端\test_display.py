#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据显示效果
"""

def test_data_processing():
    """测试数据处理效果"""
    # 您的数据
    hex_string = "4263316366722c3432323331363300685cfb0e00000197ab34af3d002030333242323032352d30362d32362031363a30323a34332c32312e32313138333938362c4e2c3131342e34343233353731392c45"
    
    print("测试数据处理效果")
    print("=" * 50)
    
    # 转换为字节
    data = bytes.fromhex(hex_string)
    
    print(f"原始十六进制: {hex_string}")
    print(f"数据长度: {len(data)} 字节")
    print()
    
    # 模拟UDP服务端的简化处理逻辑
    try:
        # 尝试UTF-8解码
        text_data = data.decode('utf-8')
        print(f"UTF-8解码结果: {text_data}")
        print()
        print("UDP服务端将显示:")
        print(f"[2024-06-26 16:02:43.123] 来自 192.168.1.100:12345 | {text_data}")
    except UnicodeDecodeError:
        # 尝试ASCII解码（忽略错误）
        ascii_data = data.decode('ascii', errors='ignore')
        print(f"ASCII解码结果: {ascii_data}")
        print()
        print("UDP服务端将显示:")
        print(f"[2024-06-26 16:02:43.123] 来自 192.168.1.100:12345 | {ascii_data}")

if __name__ == "__main__":
    test_data_processing()
