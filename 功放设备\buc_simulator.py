#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
BUC功放设备模拟器
根据L-BUC设备软件指令集实现的串口设备模拟器，用于模拟功放设备进行调试。
"""

import argparse
import logging
import random
import serial
import threading
import time
from binascii import hexlify
from serial.tools.list_ports import comports

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('BUCSimulator')

class BUCSimulator:
    """BUC功放设备模拟器类"""
    
    def __init__(self, port, baudrate=9600, verbose=False):
        """
        初始化BUC模拟器
        
        Args:
            port (str): 串口名称
            baudrate (int): 波特率，默认9600
            verbose (bool): 是否显示详细日志
        """
        self.port_name = port
        self.baudrate = baudrate
        self.port = None
        self.is_running = False
        
        # 设备基本状态
        self.power_status = True  # 功放开关状态，初始状态设为开启
        
        # 设备参数 - 按照指令集规范
        self.attenuation = 5       # 衰减参数（0~20）
        self.temperature = 25.0    # 温度（-30~+70℃）
        self.rf_power = 30.0       # 射频输出功率（0.0~50.0 dBm）
        
        # BUC状态位 - 按照指令集规范
        self.freq_locked = True    # 频率锁定状态（1=正常，0=异常）
        self.fan_status = True     # 风扇开关状态（1=开启，0=关闭）
        self.temp_normal = True    # 温度正常状态（1=正常，0=异常）
        
        # 其他监控参数（额外信息，非指令集要求）
        self.voltage = 12.0        # 电压（伏特）
        self.current = 2.0         # 电流（安培）
        self.error_code = 0        # 错误代码
        
        # 调试模式
        self.debug_mode = False    # 特殊调试模式，不进行CRC校验
        
        # 日志级别
        if verbose:
            logger.setLevel(logging.DEBUG)
    
    def start(self):
        """启动模拟器"""
        try:
            # 打开串口
            self.port = serial.Serial(
                port=self.port_name,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=1
            )
            
            logger.info(f"串口 {self.port_name} 已打开，波特率 {self.baudrate}")
            self.is_running = True
            
            # 启动监听线程
            self.listen_thread = threading.Thread(target=self._listen_thread)
            self.listen_thread.daemon = True
            self.listen_thread.start()
            
            # 启动状态更新线程
            self.status_thread = threading.Thread(target=self._status_update_thread)
            self.status_thread.daemon = True
            self.status_thread.start()
            
            logger.info("BUC模拟器已启动")
            logger.info("设备状态: 功放已开启，模拟状态实时变化中...")
            
            # 显示当前状态信息
            self._print_status_info()
            
            # 主线程循环 - 每30秒显示一次当前状态
            try:
                while self.is_running:
                    time.sleep(30)
                    self._print_status_info()
            except KeyboardInterrupt:
                logger.info("接收到退出信号")
                self.stop()
                
        except serial.SerialException as e:
            logger.error(f"串口错误: {e}")
            return False
        
        return True
    
    def _print_status_info(self):
        """打印当前设备状态信息"""
        # 计算BUC状态字节
        status_byte = self._get_buc_status_byte()
        
        status_str = (
            f"\n当前设备状态:\n"
            f"功放: {'开启' if self.power_status else '关闭'}\n"
            f"衰减: {self.attenuation} (0~20)\n"
            f"温度: {self.temperature:.1f}°C (-30~+70°C)\n"
            f"射频功率: {self.rf_power:.1f} dBm (0.0~50.0 dBm)\n"
            f"电压: {self.voltage:.1f}V\n"
            f"电流: {self.current:.1f}A\n"
            f"状态字节: 0x{status_byte:02X} ("
            f"频率{'锁定' if self.freq_locked else '异常'}, "
            f"风扇{'开启' if self.fan_status else '关闭'}, "
            f"温度{'正常' if self.temp_normal else '异常'}, "
            f"功放{'开启' if self.power_status else '关闭'})"
        )
        print(status_str)
    
    def stop(self):
        """停止模拟器"""
        self.is_running = False
        if self.port and self.port.is_open:
            self.port.close()
            logger.info(f"串口 {self.port_name} 已关闭")
        logger.info("BUC模拟器已停止")
    
    def _listen_thread(self):
        """串口监听线程"""
        logger.info("串口监听线程已启动")
        
        while self.is_running and self.port and self.port.is_open:
            try:
                # 检查是否有数据可读
                if self.port.in_waiting > 0:
                    # 读取帧头
                    frame_header = self.port.read(1)
                    
                    # 验证帧头
                    if frame_header and frame_header[0] == 0x16:
                        # 读取帧长
                        frame_length_bytes = self.port.read(1)
                        if not frame_length_bytes:
                            continue
                            
                        frame_length = frame_length_bytes[0]
                        logger.debug(f"帧长: {frame_length}")
                        
                        # 读取数据部分
                        data = self.port.read(frame_length + 1)  # +1 for CRC
                        if len(data) < frame_length + 1:
                            logger.warning(f"接收到不完整的数据: {hexlify(data).decode()}")
                            continue
                        
                        # 构建完整帧
                        complete_frame = frame_header + frame_length_bytes + data
                        
                        # 详细记录每个字节
                        byte_details = []
                        for i, b in enumerate(complete_frame):
                            if i == 0:
                                byte_details.append(f"{hex(b)}(帧头)")
                            elif i == 1:
                                byte_details.append(f"{hex(b)}(帧长)")
                            elif i == len(complete_frame) - 1:
                                byte_details.append(f"{hex(b)}(CRC)")
                            else:
                                byte_details.append(f"{hex(b)}(数据{i-1})")
                        
                        logger.debug(f"接收到命令详情: {' '.join(byte_details)}")
                        logger.debug(f"接收到命令: {hexlify(complete_frame).decode()}")
                        
                        # 提取数据部分（不包括CRC）
                        command_data = data[:frame_length]
                        received_crc = data[frame_length]
                        
                        # 记录命令数据和接收到的CRC
                        logger.debug(f"命令数据: {hexlify(command_data).decode()}")
                        logger.debug(f"接收到的CRC: {hex(received_crc)}")
                        
                        # 验证CRC
                        calculated_crc = self._calculate_crc(command_data)
                        logger.debug(f"计算的CRC: {hex(calculated_crc)}")
                        
                        # 特殊命令处理 - 对于指令集中定义的特殊命令格式
                        special_command_handling = False
                        
                        # 检查是否为BUC参数查询命令 (16 03 A2 55 F7)
                        if (len(command_data) >= 2 and 
                            command_data[0] == 0xA2 and 
                            command_data[1] == 0x55):
                            
                            logger.debug("检测到BUC参数查询命令: A2 55")
                            
                            # 验证CRC是否匹配
                            if received_crc == calculated_crc or self.debug_mode:
                                logger.debug("BUC参数查询命令CRC校验成功，处理该命令")
                                special_command_handling = True
                                self._handle_buc_query()
                        
                        # 检查是否为连接确认命令 (16 03 A1 55 F4)
                        elif (len(command_data) >= 2 and 
                              command_data[0] == 0xA1 and 
                              command_data[1] == 0x55):
                            
                            logger.debug("检测到连接确认命令: A1 55")
                            
                            # 验证CRC是否匹配
                            if received_crc == calculated_crc or self.debug_mode:
                                logger.debug("连接确认命令CRC校验成功，处理该命令")
                                special_command_handling = True
                                self._handle_connection_confirm()
                        
                        # 常规CRC校验和命令处理
                        if not special_command_handling:
                            if calculated_crc == received_crc or self.debug_mode:
                                # CRC校验成功或者处于调试模式
                                if calculated_crc != received_crc and self.debug_mode:
                                    logger.warning(f"调试模式: 忽略CRC校验失败: 计算值={hex(calculated_crc)}, 接收值={hex(received_crc)}")
                                else:
                                    logger.debug("CRC校验成功")
                                
                                # 处理命令
                                self._process_command(command_data)
                            else:
                                logger.warning(f"CRC校验失败: 计算值={hex(calculated_crc)}, 接收值={hex(received_crc)}")
                                
                                # 尝试解释用户命令的意图
                                self._analyze_failed_command(command_data, received_crc)
                    
            except serial.SerialException as e:
                logger.error(f"串口读取错误: {e}")
                self.is_running = False
                break
            except Exception as e:
                logger.error(f"处理命令时发生错误: {e}")
            
            # 短暂休眠，避免CPU占用过高
            time.sleep(0.01)
    
    def _analyze_failed_command(self, command_data, received_crc):
        """
        分析CRC校验失败的命令，尝试理解用户意图
        
        Args:
            command_data (bytes): 命令数据
            received_crc (int): 接收到的CRC
        """
        logger.debug(f"分析CRC校验失败的命令: {hexlify(command_data).decode()}")
        
        # 检查命令数据长度
        if not command_data:
            logger.debug("命令数据为空，无法分析")
            return
        
        # 检查是否为指令集中定义的特殊命令
        first_byte = command_data[0]
        
        # 尝试识别命令类型
        if first_byte == 0xA1:
            logger.debug("可能是连接确认命令")
            if len(command_data) >= 2:
                if command_data[1] == 0x55:
                    # 标准连接确认命令: A1 55
                    correct_data = bytes([0xA1, 0x55])
                    correct_crc = self._calculate_crc(correct_data)
                    logger.debug(f"正确的连接确认命令CRC应为: {hex(correct_crc)}")
                    logger.debug(f"正确的连接确认命令: 16 03 A1 55 {hex(correct_crc)[2:]}")
                else:
                    logger.debug(f"连接确认命令参数错误: 期望0x55，实际{hex(command_data[1])}")
            else:
                logger.debug("连接确认命令数据不完整")
        
        elif first_byte == 0xA2:
            logger.debug("可能是BUC参数查询命令")
            if len(command_data) >= 2:
                if command_data[1] == 0x55:
                    # 标准BUC参数查询命令: A2 55
                    correct_data = bytes([0xA2, 0x55])
                    correct_crc = self._calculate_crc(correct_data)
                    logger.debug(f"正确的BUC参数查询命令CRC应为: {hex(correct_crc)}")
                    logger.debug(f"正确的BUC参数查询命令: 16 03 A2 55 {hex(correct_crc)[2:]}")
                else:
                    logger.debug(f"BUC参数查询命令参数错误: 期望0x55，实际{hex(command_data[1])}")
            else:
                logger.debug("BUC参数查询命令数据不完整")
        
        elif first_byte == 0xA3:
            logger.debug("可能是衰减设置命令")
            if len(command_data) >= 2:
                if command_data[1] == 0x55:
                    if len(command_data) >= 3:
                        # 标准衰减设置命令: A3 55 XX
                        attenuation_value = command_data[2]
                        correct_data = bytes([0xA3, 0x55, attenuation_value])
                        correct_crc = self._calculate_crc(correct_data)
                        logger.debug(f"正确的衰减设置命令CRC应为: {hex(correct_crc)}")
                        logger.debug(f"正确的衰减设置命令: 16 04 A3 55 {hex(attenuation_value)[2:]} {hex(correct_crc)[2:]}")
                    else:
                        logger.debug("衰减设置命令缺少衰减值参数")
                else:
                    logger.debug(f"衰减设置命令参数错误: 期望0x55，实际{hex(command_data[1])}")
            else:
                logger.debug("衰减设置命令数据不完整")
        
        elif first_byte == 0xA4:
            logger.debug("可能是功放开关设置命令")
            if len(command_data) >= 2:
                if command_data[1] == 0x55:
                    if len(command_data) >= 3:
                        # 标准功放开关设置命令: A4 55 XX
                        control_value = command_data[2]
                        correct_data = bytes([0xA4, 0x55, control_value])
                        correct_crc = self._calculate_crc(correct_data)
                        
                        if control_value == 0x55:  # 开启功放
                            logger.debug(f"正确的功放开启命令CRC应为: {hex(correct_crc)}")
                            logger.debug(f"正确的功放开启命令: 16 04 A4 55 55 {hex(correct_crc)[2:]}")
                        elif control_value == 0xAA:  # 关闭功放
                            logger.debug(f"正确的功放关闭命令CRC应为: {hex(correct_crc)}")
                            logger.debug(f"正确的功放关闭命令: 16 04 A4 55 AA {hex(correct_crc)[2:]}")
                        else:
                            logger.debug(f"功放控制值错误: 期望0x55(开启)或0xAA(关闭)，实际{hex(control_value)}")
                    else:
                        logger.debug("功放开关设置命令缺少控制值参数")
                else:
                    logger.debug(f"功放开关设置命令参数错误: 期望0x55，实际{hex(command_data[1])}")
            else:
                logger.debug("功放开关设置命令数据不完整")
        
        else:
            logger.debug(f"未识别的命令类型: {hex(first_byte)}")
        
        # 提供标准命令示例
        logger.debug("\n标准命令格式示例:")
        logger.debug("1. 连接确认: 16 03 A1 55 F4")
        logger.debug("2. BUC参数查询: 16 03 A2 55 F7")
        logger.debug("3. 衰减设置(XX为衰减值): 16 04 A3 55 XX YY")
        logger.debug("4. 功放开启: 16 04 A4 55 55 A4")
        logger.debug("5. 功放关闭: 16 04 A4 55 AA 5B")
    
    def _status_update_thread(self):
        """状态更新线程，模拟设备状态变化"""
        logger.info("状态更新线程已启动")
        
        while self.is_running:
            # 始终模拟状态变化，无论功放是否开启，但开启和关闭状态下变化幅度不同
            if self.power_status:
                # 功放开启状态 - 大幅度变化
                
                # 衰减参数波动 (3-8)
                self.attenuation = random.randint(3, 8)
                
                # 温度波动 (24-30°C)
                self.temperature = round(25.0 + random.uniform(-1.0, 5.0), 1)
                
                # 射频功率波动 (25.0-35.0 dBm)
                self.rf_power = round(30.0 + random.uniform(-5.0, 5.0), 1)
                
                # 电压波动 (11.5-12.5V)
                self.voltage = round(12.0 + random.uniform(-0.5, 0.5), 1)
                
                # 电流波动 (1.8-2.5A)
                self.current = round(2.0 + random.uniform(-0.2, 0.5), 1)
                
                # 状态位变化 - 小概率出现异常
                self.freq_locked = random.random() > 0.02  # 98%概率正常
                self.fan_status = random.random() > 0.01   # 99%概率正常
                self.temp_normal = self.temperature < 28.0  # 温度超过28度时异常
                
                # 小概率生成错误代码
                if random.random() < 0.02:  # 2%概率
                    self.error_code = random.randint(1, 5)
                else:
                    self.error_code = 0
            else:
                # 功放关闭状态 - 小幅度变化
                
                # 衰减参数固定 (0-2)
                self.attenuation = random.randint(0, 2)
                
                # 温度缓慢降低并波动 (22-24°C)
                self.temperature = round(max(22.0, self.temperature * 0.99) + random.uniform(-0.2, 0.2), 1)
                
                # 射频功率降低 (0.0-5.0 dBm)
                self.rf_power = round(random.uniform(0.0, 5.0), 1)
                
                # 电压小幅波动 (11.9-12.1V)
                self.voltage = round(12.0 + random.uniform(-0.1, 0.1), 1)
                
                # 电流小幅波动 (0.1-0.3A)
                self.current = round(0.2 + random.uniform(-0.1, 0.1), 1)
                
                # 状态位变化 - 功放关闭状态
                self.freq_locked = True  # 频率锁定正常
                self.fan_status = random.random() > 0.5  # 风扇可能开可能关
                self.temp_normal = True  # 温度正常
                
                # 没有错误
                self.error_code = 0
            
            # 每秒更新一次状态
            time.sleep(1)
    
    def _process_command(self, data):
        """
        处理接收到的命令
        
        Args:
            data (bytes): 命令数据（不包括帧头、长度和CRC）
        """
        # 确保数据长度至少为1
        if not data or len(data) < 1:
            logger.warning("命令数据为空或太短")
            return
        
        # 根据指令集规范识别命令
        # 标准命令格式: [操作码] [参数...]
        cmd_type = data[0]
        
        try:
            # 标准命令类型处理
            if cmd_type == 0x31:  # 设备查询
                self._handle_query()
            elif cmd_type == 0x32:  # BUC单独查询
                self._handle_buc_query()
            elif cmd_type == 0x33:  # 变量设置
                self._handle_var_setting(data)
            elif cmd_type == 0x34:  # 功放开关设置
                self._handle_power_control(data)
            # 特殊命令处理 - 按照指令集规范
            elif cmd_type == 0xA1 and len(data) >= 2 and data[1] == 0x55:
                # 连接确认命令: A1 55
                logger.info("接收到连接确认命令: A1 55")
                self._handle_connection_confirm()
            elif cmd_type == 0xA2 and len(data) >= 2 and data[1] == 0x55:
                # BUC参数查询命令: A2 55
                logger.info("接收到BUC参数查询命令: A2 55")
                self._handle_buc_query()
            elif cmd_type == 0xA3 and len(data) >= 2 and data[1] == 0x55:
                # 衰减设置命令: A3 55 XX
                if len(data) >= 3:
                    attenuation_value = data[2]
                    logger.info(f"接收到衰减设置命令: A3 55 {hex(attenuation_value)}")
                    self._handle_var_setting(bytes([0x33, 0xA3, attenuation_value]))
                else:
                    logger.warning("衰减设置命令数据不完整")
            elif cmd_type == 0xA4 and len(data) >= 2 and data[1] == 0x55:
                # 功放开关设置命令: A4 55 XX
                if len(data) >= 3:
                    control_value = data[2]
                    logger.info(f"接收到功放开关设置命令: A4 55 {hex(control_value)}")
                    if control_value == 0x55:  # 开启功放
                        self._handle_power_control(bytes([0x34, 0xA4]))
                    elif control_value == 0xAA:  # 关闭功放
                        self._handle_power_control(bytes([0x34, 0xA5]))
                    else:
                        logger.warning(f"未知的功放控制值: {hex(control_value)}")
                else:
                    logger.warning("功放开关设置命令数据不完整")
            else:
                logger.warning(f"未知命令类型: {hex(cmd_type)}")
                
                # 对于非标准命令类型，尝试解释
                if cmd_type == 0xA2:
                    logger.warning("接收到0xA2作为命令类型，但缺少参数0x55")
                    logger.warning("正确的BUC参数查询命令应为: 16 03 A2 55 F7")
                    
                    # 如果处于调试模式，可以尝试按BUC单独查询处理
                    if self.debug_mode:
                        logger.info("调试模式: 将0xA2解释为BUC参数查询命令")
                        self._handle_buc_query()
                
                elif cmd_type == 0xA3 or cmd_type == 0xA4 or cmd_type == 0xA5:
                    logger.warning("接收到功放控制或变量设置的参数作为命令类型，但格式不正确")
        except Exception as e:
            logger.error(f"处理命令 {hex(cmd_type)} 时发生错误: {e}")
    
    def _handle_query(self):
        """处理设备查询命令"""
        logger.info("处理设备查询命令")
        
        # 根据协议，设备查询响应为 11 03 31 AA FB
        response = self._create_response(0x11, 0x03, 0x31, 0xAA)
        
        self._send_response(response)
    
    def _handle_buc_query(self):
        """处理BUC单独查询命令"""
        logger.info("处理BUC单独查询命令")
        
        # 按照指令集规范生成6个状态字节
        # 1. 衰减参数 (0~20)
        attenuation_byte = self.attenuation
        
        # 2. 温度参数 (-30~+70℃ 映射到 0~100)
        # 将温度从 -30~+70℃ 映射到 0~100
        temp_value = int((self.temperature + 30) / 100 * 100)
        temp_value = max(0, min(100, temp_value))  # 确保在0~100范围内
        
        # 3-4. 射频输出功率 (0.0~50.0 dBm 映射到 0~500)
        # 将功率值乘以10转为整数
        power_value = int(self.rf_power * 10)
        power_high = (power_value >> 8) & 0xFF  # 高字节
        power_low = power_value & 0xFF          # 低字节
        
        # 5. NULL (保留字节)
        null_byte = 0x00
        
        # 6. BUC状态
        status_byte = self._get_buc_status_byte()
        
        # 组合所有状态字节
        status_bytes = [
            0x52,             # 响应操作码
            0x55,             # 固定值
            attenuation_byte, # 衰减参数
            temp_value,       # 温度参数
            power_high,       # 射频输出功率高字节
            power_low,        # 射频输出功率低字节
            null_byte,        # 保留字节
            status_byte       # BUC状态字节
        ]
        
        # 响应: 11 09 52 55 [6字节命令数据] YY
        response = self._create_response(0x11, len(status_bytes), *status_bytes)
        
        self._send_response(response)
    
    def _handle_var_setting(self, data):
        """
        处理变量设置命令
        
        Args:
            data (bytes): 命令数据
        """
        logger.info("处理变量设置命令")
        
        # 检查数据长度
        if len(data) < 3:
            logger.warning("变量设置命令数据不完整")
            return
        
        # 提取变量标识和值
        var_id = data[1]
        var_value = data[2]
        
        logger.debug(f"设置变量: ID={hex(var_id)}, 值={hex(var_value)}")
        
        # 随机生成两个响应字节
        response_data = [
            0xA3,  # 固定值
            0x55,  # 固定值
            random.randint(0, 255),  # 随机值
            random.randint(0, 255)   # 随机值
        ]
        
        # 响应: 11 04 A3 55 XX YY
        response = self._create_response(0x11, 0x04, *response_data)
        
        self._send_response(response)
    
    def _handle_power_control(self, data):
        """
        处理功放开关设置命令
        
        Args:
            data (bytes): 命令数据
        """
        # 检查数据长度
        if len(data) < 2:
            logger.warning("功放开关命令数据不完整")
            return
        
        control_byte = data[1]
        
        if control_byte == 0xA4:  # 开启功放
            logger.info("处理功放开启命令")
            self.power_status = True
            print("\n功放已开启")
            
            # 响应: 11 04 54 55 55 54
            response = self._create_response(0x11, 0x04, 0x54, 0x55, 0x55, 0x54)
            
        elif control_byte == 0xA5:  # 关闭功放
            logger.info("处理功放关闭命令")
            self.power_status = False
            print("\n功放已关闭")
            
            # 响应: 11 04 54 AA AB
            response = self._create_response(0x11, 0x04, 0x54, 0xAA, 0xAB)
            
        else:
            logger.warning(f"未知的功放控制命令: {hex(control_byte)}")
            return
        
        self._send_response(response)
    
    def _create_response(self, header, length, *data_bytes):
        """
        创建响应帧
        
        Args:
            header (int): 帧头
            length (int): 数据长度
            *data_bytes: 数据字节
            
        Returns:
            bytes: 完整的响应帧
        """
        # 构建数据部分
        data = bytes(data_bytes)
        
        # 计算CRC
        crc = self._calculate_crc(data)
        
        # 构建完整帧
        response = bytes([header, length]) + data + bytes([crc])
        
        logger.debug(f"创建响应: {hexlify(response).decode()}")
        
        return response
    
    def _send_response(self, response):
        """
        发送响应
        
        Args:
            response (bytes): 响应数据
        """
        if self.port and self.port.is_open:
            try:
                self.port.write(response)
                logger.debug(f"已发送响应: {hexlify(response).decode()}")
            except serial.SerialException as e:
                logger.error(f"发送响应时发生错误: {e}")
    
    def _calculate_crc(self, data):
        """
        计算CRC校验值（所有数据字节的异或值）
        
        Args:
            data (bytes): 要计算CRC的数据
            
        Returns:
            int: CRC校验值
        """
        crc = 0
        for b in data:
            crc ^= b
        return crc

    def _get_buc_status_byte(self):
        """
        生成BUC状态字节，按照指令集规范
        状态位定义（1表示正常/打开，0表示异常/关闭）：
        - 位1: 频率锁定
        - 位2: 风扇开关
        - 位3: 温度正常
        - 位4: 功放开关
        - 位5-6: NULL
        
        Returns:
            int: BUC状态字节
        """
        status_byte = 0
        if self.freq_locked:
            status_byte |= 0x01  # 频率锁定位
        if self.fan_status:
            status_byte |= 0x02  # 风扇开关位
        if self.temp_normal:
            status_byte |= 0x04  # 温度正常位
        if self.power_status:
            status_byte |= 0x08  # 功放开关位
        
        return status_byte

    def _handle_connection_confirm(self):
        """处理连接确认命令"""
        logger.info("处理连接确认命令")
        
        # 根据协议，连接确认响应为 11 03 51 AA FB
        response = self._create_response(0x11, 0x03, 0x51, 0xAA)
        
        self._send_response(response)


def list_available_ports():
    """列出可用的串口"""
    available_ports = list(comports())
    if not available_ports:
        print("未检测到可用串口")
        return []
    
    print("可用串口列表:")
    for i, port in enumerate(available_ports):
        print(f"{i+1}. {port.device} - {port.description}")
    
    return [port.device for port in available_ports]


def main():
    """主函数"""
    print("="*50)
    print("BUC功放设备模拟器")
    print("根据L-BUC设备软件指令集实现的串口设备模拟器")
    print("="*50)
    
    # 列出可用串口
    available_ports = list_available_ports()
    
    if not available_ports:
        print("\n请插入串口设备后重试，或手动输入串口名称")
        port = input("\n请输入串口名称（例如COM1）: ")
    else:
        # 用户选择串口或手动输入
        choice = input("\n请选择串口编号，或直接输入串口名称: ")
        
        try:
            # 尝试作为索引处理
            idx = int(choice) - 1
            if 0 <= idx < len(available_ports):
                port = available_ports[idx]
            else:
                print(f"无效的选择，请输入1-{len(available_ports)}之间的数字")
                port = input("请手动输入串口名称: ")
        except ValueError:
            # 用户直接输入了串口名称
            port = choice
    
    # 用户选择波特率
    baudrate_str = input("\n请输入波特率 [9600]: ")
    baudrate = int(baudrate_str) if baudrate_str.strip() else 9600
    
    # 是否显示详细日志
    verbose = input("\n是否显示详细日志? (y/n) [n]: ").lower() == 'y'
    
    # 是否启用调试模式（不进行CRC校验）
    debug_mode = input("\n是否启用调试模式（不进行CRC校验）? (y/n) [n]: ").lower() == 'y'
    
    print(f"\n正在启动模拟器，串口: {port}, 波特率: {baudrate}")
    if debug_mode:
        print("调试模式已启用: CRC校验已禁用")
    print("按Ctrl+C可停止模拟器")
    print("-"*50)
    
    # 创建并启动模拟器
    simulator = BUCSimulator(port, baudrate, verbose)
    simulator.debug_mode = debug_mode
    simulator.start()


if __name__ == '__main__':
    main() 