#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UDP数据解析工具 - 用于解析和转换接收到的十六进制数据
"""

import struct
import binascii
from datetime import datetime


class DataParser:
    def __init__(self):
        pass
    
    def parse_hex_data(self, hex_string):
        """解析十六进制数据"""
        print(f"原始十六进制数据: {hex_string}")
        print(f"数据长度: {len(hex_string)} 字符 ({len(hex_string)//2} 字节)")
        print("-" * 60)
        
        try:
            # 转换为字节数组
            data_bytes = bytes.fromhex(hex_string)
            
            # 尝试不同的解析方法
            self._try_ascii_decode(data_bytes)
            self._try_utf8_decode(data_bytes)
            self._try_mixed_decode(data_bytes)
            self._analyze_structure(data_bytes)
            
        except Exception as e:
            print(f"解析错误: {e}")
    
    def _try_ascii_decode(self, data_bytes):
        """尝试ASCII解码"""
        print("\n=== ASCII解码尝试 ===")
        try:
            ascii_text = data_bytes.decode('ascii', errors='replace')
            print(f"ASCII文本: {repr(ascii_text)}")
            
            # 显示可打印字符
            printable = ''.join(c if c.isprintable() else f'\\x{ord(c):02x}' for c in ascii_text)
            print(f"可打印部分: {printable}")
            
        except Exception as e:
            print(f"ASCII解码失败: {e}")
    
    def _try_utf8_decode(self, data_bytes):
        """尝试UTF-8解码"""
        print("\n=== UTF-8解码尝试 ===")
        try:
            utf8_text = data_bytes.decode('utf-8', errors='replace')
            print(f"UTF-8文本: {repr(utf8_text)}")
            
        except Exception as e:
            print(f"UTF-8解码失败: {e}")
    
    def _try_mixed_decode(self, data_bytes):
        """尝试混合解码（部分ASCII + 部分二进制）"""
        print("\n=== 混合数据分析 ===")
        
        # 查找可能的文本段
        text_segments = []
        current_text = ""
        
        for i, byte_val in enumerate(data_bytes):
            if 32 <= byte_val <= 126:  # 可打印ASCII字符
                current_text += chr(byte_val)
            else:
                if len(current_text) >= 3:  # 至少3个字符才认为是有意义的文本
                    text_segments.append((i - len(current_text), current_text))
                current_text = ""
        
        # 处理最后的文本段
        if len(current_text) >= 3:
            text_segments.append((len(data_bytes) - len(current_text), current_text))
        
        if text_segments:
            print("发现的文本段:")
            for pos, text in text_segments:
                print(f"  位置 {pos}: '{text}'")
        else:
            print("未发现明显的文本段")
    
    def _analyze_structure(self, data_bytes):
        """分析数据结构"""
        print("\n=== 数据结构分析 ===")
        
        # 按字节显示十六进制
        print("十六进制字节:")
        for i in range(0, len(data_bytes), 16):
            chunk = data_bytes[i:i+16]
            hex_part = ' '.join(f'{b:02x}' for b in chunk)
            ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
            print(f"{i:04x}: {hex_part:<48} |{ascii_part}|")
        
        # 尝试识别常见的数据模式
        self._identify_patterns(data_bytes)
    
    def _identify_patterns(self, data_bytes):
        """识别数据模式"""
        print("\n=== 模式识别 ===")
        
        # 查找逗号分隔的数据（CSV格式）
        if b',' in data_bytes:
            print("发现逗号分隔符，可能是CSV格式数据")
            try:
                # 尝试解码包含逗号的部分
                text_data = data_bytes.decode('ascii', errors='ignore')
                if ',' in text_data:
                    parts = text_data.split(',')
                    print(f"CSV字段数量: {len(parts)}")
                    for i, part in enumerate(parts):
                        if part.strip():
                            print(f"  字段 {i+1}: '{part.strip()}'")
            except:
                pass
        
        # 查找日期时间格式
        datetime_patterns = [
            b'20', b'19',  # 年份开头
        ]
        
        for pattern in datetime_patterns:
            if pattern in data_bytes:
                pos = data_bytes.find(pattern)
                print(f"在位置 {pos} 发现可能的日期时间数据")
        
        # 查找GPS坐标格式（度分格式）
        if b'.' in data_bytes:
            print("发现小数点，可能包含GPS坐标或其他数值数据")
        
        # 查找特定的字符模式
        if b'N' in data_bytes and b'E' in data_bytes:
            print("发现N和E字符，可能是GPS坐标的方向标识")


def main():
    """主函数"""
    parser = DataParser()
    
    # 您提供的数据
    hex_data = "4263316366722c3432323331363300685cfb0e00000197ab34af3d002030333242323032352d30362d32362031363a30323a34332c32312e32313138333938362c4e2c3131342e34343233353731392c45"
    
    print("UDP数据解析工具")
    print("=" * 60)
    
    parser.parse_hex_data(hex_data)
    
    print("\n" + "=" * 60)
    print("解析完成")
    
    # 交互模式
    print("\n输入其他十六进制数据进行解析（输入 'quit' 退出）:")
    while True:
        try:
            user_input = input("\n十六进制数据> ").strip()
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            if user_input:
                # 移除可能的空格和分隔符
                clean_hex = user_input.replace(' ', '').replace('-', '').replace(':', '')
                parser.parse_hex_data(clean_hex)
        except KeyboardInterrupt:
            break
        except EOFError:
            break
    
    print("再见！")


if __name__ == "__main__":
    main()
