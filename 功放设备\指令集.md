以下是根据文档 **《BUC监控指令集（2024简版）》** 整理的 Markdown 版本：

---

# BUC 监控指令集（2024 简版）

## 一、串口通信说明

* **波特率**：9600
* **校验位**：无
* **数据位**：8
* **停止位**：1

---

## 二、命令结构

| 字段    | 长度  | 说明                  |
| ----- | --- | ------------------- |
| 命令头   | 1字节 | 固定为 `0x16` 或 `0x11` |
| 帧长    | 1字节 | 从操作码到 CRC 校验码为止的字节数 |
| 操作码   | 1字节 | 表示不同命令类型            |
| 命令数据  | 可变  | 根据操作码不同而变化          |
| CRC校验 | 1字节 | 从操作码到命令数据为止的 CRC 校验 |

---

## 三、指令与响应

### 1. 连接确认

* **发送**：`16 03 A1 55 F4`
* **BUC回复**：`11 03 51 AA FB`

---

### 2. BUC参数查询

* **发送**：`16 03 A2 55 F7`
* **BUC回复**：`11 09 52 55 [6字节命令数据] YY`

#### 6字节命令数据字段说明：

| 字节位置 | 含义     | 说明                        |
| ---- | ------ | ------------------------- |
| 1    | 衰减参数   | 单字节，范围 0\~20              |
| 2    | 温度参数   | 单字节，范围 -30℃~~+70℃（0~~100） |
| 3-4  | 射频输出功率 | 双字节，0.0~~50.0 dBm（0~~500） |
| 5    | NULL   | 保留                        |
| 6    | BUC状态  | 单字节，状态位定义如下               |

#### 状态位定义（1 表示正常 / 打开，0 表示异常 / 关闭）：

| 位   | 状态项  |
| --- | ---- |
| 1   | 频率锁定 |
| 2   | 风扇开关 |
| 3   | 温度正常 |
| 4   | 功放开关 |
| 5-6 | NULL |

---

### 3. 衰减设置

* **发送**：`16 04 A3 55 XX YY`

  * `XX`：衰减值（0\~20）
  * `YY`：CRC校验码

* **BUC回复**：`11 04 53 55 XX YY`

---

### 4. 功放开关设置

* **打开射频输出**

  * **发送**：`16 04 A4 55 55 A4`
  * **BUC回复**：`11 04 54 55 55 54`

* **关闭射频输出**

  * **发送**：`16 04 A4 55 AA 5B`
  * **BUC回复**：`11 04 54 55 AA AB`

---

如需我导出为 `.md` 文件，请告知我是否需要下载链接。
