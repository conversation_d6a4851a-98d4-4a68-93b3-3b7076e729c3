# 北斗设备模拟器

这是一个用于模拟北斗/GPS设备发送NMEA-0183协议格式定位数据的程序。

## 功能特点

- 支持生成标准NMEA-0183协议格式的GNRMC/BDRMC数据
- 可通过串口发送模拟数据
- 支持多种轨迹模式：直线、圆形和自定义轨迹
- 可配置发送频率、起始位置等参数

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

```bash
python main.py --port COM3 --baud 9600 --freq 1 --mode circle
```

### 参数说明

- `--port`: 串口名称，例如COM3（Windows）或/dev/ttyUSB0（Linux）
- `--baud`: 波特率，默认9600
- `--freq`: 数据发送频率（Hz），默认1Hz
- `--mode`: 轨迹模式，可选值：line（直线）、circle（圆形）、file（从文件加载）
- `--file`: 当mode=file时，指定轨迹文件路径
- `--start-lat`: 起始纬度，默认为39.9042°N（北京）
- `--start-lon`: 起始经度，默认为116.4074°E（北京）
- `--speed`: 模拟速度，单位节，默认5.0
- `--type`: NMEA数据类型，可选值：GNRMC、BDRMC，默认BDRMC

## 配置文件

可以通过修改`config.json`文件来设置默认参数。 