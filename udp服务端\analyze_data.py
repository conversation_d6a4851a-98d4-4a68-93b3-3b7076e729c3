#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析数据中的控制字符
"""

def analyze_control_chars():
    """分析控制字符"""
    hex_string = "4263316366722c3432323331363300685cfb0e00000197ab34af3d002030333242323032352d30362d32362031363a30323a34332c32312e32313138333938362c4e2c3131342e34343233353731392c45"
    
    data = bytes.fromhex(hex_string)
    
    print("数据分析:")
    print("=" * 50)
    
    # 显示每个字节的十六进制和ASCII对应
    print("字节分析 (十六进制 -> ASCII):")
    for i, byte_val in enumerate(data):
        if 32 <= byte_val <= 126:  # 可打印ASCII字符
            char = chr(byte_val)
            print(f"位置 {i:2d}: 0x{byte_val:02x} -> '{char}'")
        else:
            print(f"位置 {i:2d}: 0x{byte_val:02x} -> [控制字符]")
    
    print("\n" + "=" * 50)
    
    # 找到 h]N 对应的字节
    ascii_data = data.decode('ascii', errors='ignore')
    print(f"完整ASCII解码: {repr(ascii_data)}")
    
    # 查找 h]N 的位置
    h_pos = ascii_data.find('h')
    if h_pos != -1:
        print(f"\n'h' 在位置 {h_pos}")
        # 显示h后面几个字节
        for i in range(h_pos, min(h_pos + 10, len(data))):
            byte_val = data[i]
            if 32 <= byte_val <= 126:
                char = chr(byte_val)
                print(f"位置 {i}: 0x{byte_val:02x} = '{char}'")
            else:
                print(f"位置 {i}: 0x{byte_val:02x} = [控制字符 {byte_val}]")

if __name__ == "__main__":
    analyze_control_chars()
