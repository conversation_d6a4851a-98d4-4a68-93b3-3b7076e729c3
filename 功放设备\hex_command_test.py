#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
十六进制命令测试脚本
专门用于分析和测试BUC功放设备指令集命令
"""

import serial
import time
import logging
from binascii import hexlify, unhexlify

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('HexCommandTest')

def calculate_crc(data):
    """
    计算CRC校验值（所有数据字节的异或值）
    
    Args:
        data (bytes): 要计算CRC的数据
        
    Returns:
        int: CRC校验值
    """
    crc = 0
    for b in data:
        crc ^= b
    return crc

def hex_string_to_bytes(hex_string):
    """
    将十六进制字符串转换为字节数组
    
    Args:
        hex_string (str): 十六进制字符串，可以包含空格
        
    Returns:
        bytes: 转换后的字节数组
    """
    # 移除所有空格
    hex_string = hex_string.replace(" ", "")
    
    # 转换为字节数组
    return bytes.fromhex(hex_string)

def analyze_command(hex_command):
    """
    分析十六进制命令
    
    Args:
        hex_command (str): 十六进制命令字符串
    """
    logger.info(f"分析命令: {hex_command}")
    
    # 转换为字节数组
    cmd_bytes = hex_string_to_bytes(hex_command)
    
    # 显示每个字节
    logger.info("命令字节详情:")
    for i, b in enumerate(cmd_bytes):
        logger.info(f"  字节 {i+1}: {hex(b)}")
    
    # 检查命令长度
    if len(cmd_bytes) < 4:  # 至少需要帧头(1)+帧长(1)+数据(1)+CRC(1)
        logger.error("命令太短，无法进行有效分析")
        return
    
    # 提取命令组成部分
    frame_header = cmd_bytes[0]
    frame_length = cmd_bytes[1]
    data = cmd_bytes[2:-1]  # 提取数据部分，不包括帧头、帧长和CRC
    received_crc = cmd_bytes[-1]
    
    logger.info(f"帧头: {hex(frame_header)}")
    logger.info(f"帧长: {hex(frame_length)}")
    logger.info(f"数据: {[hex(b) for b in data]}")
    logger.info(f"接收到的CRC: {hex(received_crc)}")
    
    # 验证帧长
    if frame_length != len(data):
        logger.warning(f"帧长不匹配: 声明长度={frame_length}, 实际数据长度={len(data)}")
    
    # 计算CRC
    calculated_crc = calculate_crc(data)
    logger.info(f"计算的CRC: {hex(calculated_crc)}")
    
    # 验证CRC
    if calculated_crc == received_crc:
        logger.info("CRC校验成功！")
    else:
        logger.warning("CRC校验失败！")
        
        # 构建正确的命令
        corrected_cmd = bytes([frame_header, frame_length]) + data + bytes([calculated_crc])
        corrected_hex = hexlify(corrected_cmd).decode()
        logger.info(f"修正后的命令: {corrected_hex}")
    
    # 解析命令类型
    if len(data) > 0:
        first_byte = data[0]
        
        # 按照指令集规范解析命令
        if first_byte == 0xA1 and len(data) >= 2 and data[1] == 0x55:
            logger.info("命令类型: 连接确认命令")
        elif first_byte == 0xA2 and len(data) >= 2 and data[1] == 0x55:
            logger.info("命令类型: BUC参数查询命令")
        elif first_byte == 0xA3 and len(data) >= 2 and data[1] == 0x55:
            if len(data) >= 3:
                logger.info(f"命令类型: 衰减设置命令，衰减值: {data[2]}")
            else:
                logger.info("命令类型: 衰减设置命令（数据不完整）")
        elif first_byte == 0xA4 and len(data) >= 2 and data[1] == 0x55:
            if len(data) >= 3:
                if data[2] == 0x55:
                    logger.info("命令类型: 功放开启命令")
                elif data[2] == 0xAA:
                    logger.info("命令类型: 功放关闭命令")
                else:
                    logger.info(f"命令类型: 功放控制命令，未知控制值: {hex(data[2])}")
            else:
                logger.info("命令类型: 功放控制命令（数据不完整）")
        else:
            logger.warning(f"未识别的命令类型: {hex(first_byte)}")

def send_command(port_name, hex_command, wait_time=0.5):
    """
    发送十六进制命令并接收响应
    
    Args:
        port_name (str): 串口名称
        hex_command (str): 十六进制命令字符串
        wait_time (float): 等待响应的时间，单位秒
    """
    try:
        # 转换命令
        cmd_bytes = hex_string_to_bytes(hex_command)
        
        # 打开串口
        port = serial.Serial(
            port=port_name,
            baudrate=9600,
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=1
        )
        
        logger.info(f"已打开串口 {port_name}")
        logger.info(f"发送命令: {hex_command}")
        
        # 清空接收缓冲区
        port.reset_input_buffer()
        
        # 发送命令
        port.write(cmd_bytes)
        
        # 等待响应
        time.sleep(wait_time)
        
        # 读取响应
        if port.in_waiting > 0:
            response = port.read(port.in_waiting)
            response_hex = hexlify(response).decode()
            logger.info(f"收到响应: {response_hex}")
            
            # 解析响应
            if len(response) >= 4:  # 至少需要帧头(1)+帧长(1)+数据(1)+CRC(1)
                parse_response(response)
            else:
                logger.warning("响应数据太短，无法解析")
        else:
            logger.warning("未收到响应")
        
        # 关闭串口
        port.close()
        logger.info(f"已关闭串口 {port_name}")
        
    except serial.SerialException as e:
        logger.error(f"串口错误: {e}")
    except Exception as e:
        logger.error(f"发生错误: {e}")

def parse_response(response_bytes):
    """
    解析响应数据
    
    Args:
        response_bytes (bytes): 响应字节数组
    """
    try:
        # 提取响应组成部分
        frame_header = response_bytes[0]
        frame_length = response_bytes[1]
        data = response_bytes[2:-1]  # 提取数据部分，不包括帧头、帧长和CRC
        received_crc = response_bytes[-1]
        
        logger.info("响应解析:")
        logger.info(f"  帧头: {hex(frame_header)}")
        logger.info(f"  帧长: {hex(frame_length)}")
        logger.info(f"  数据: {[hex(b) for b in data]}")
        logger.info(f"  CRC: {hex(received_crc)}")
        
        # 验证CRC
        calculated_crc = calculate_crc(data)
        if calculated_crc == received_crc:
            logger.info("  CRC校验: 成功")
        else:
            logger.warning(f"  CRC校验: 失败 (计算值={hex(calculated_crc)}, 接收值={hex(received_crc)})")
        
        # 解析响应类型
        if len(data) > 0:
            first_byte = data[0]
            
            # 按照指令集规范解析响应
            if first_byte == 0x51 and len(data) >= 2 and data[1] == 0xAA:
                logger.info("  响应类型: 连接确认响应")
            elif first_byte == 0x52 and len(data) >= 8:
                logger.info("  响应类型: BUC参数查询响应")
                
                # 解析BUC参数
                if len(data) >= 8:
                    attenuation = data[2]
                    temperature = data[3]
                    power_high = data[4]
                    power_low = data[5]
                    power_value = (power_high << 8) | power_low
                    power_dbm = power_value / 10.0
                    status_byte = data[7]
                    
                    # 解析状态字节
                    freq_locked = bool(status_byte & 0x01)
                    fan_status = bool(status_byte & 0x02)
                    temp_normal = bool(status_byte & 0x04)
                    power_status = bool(status_byte & 0x08)
                    
                    logger.info("  BUC参数:")
                    logger.info(f"    衰减: {attenuation}")
                    logger.info(f"    温度: {temperature} (映射值，实际温度约 {temperature/100*100-30:.1f}°C)")
                    logger.info(f"    射频功率: {power_dbm:.1f} dBm")
                    logger.info(f"    状态字节: {hex(status_byte)}")
                    logger.info(f"      - 频率锁定: {'正常' if freq_locked else '异常'}")
                    logger.info(f"      - 风扇状态: {'开启' if fan_status else '关闭'}")
                    logger.info(f"      - 温度状态: {'正常' if temp_normal else '异常'}")
                    logger.info(f"      - 功放状态: {'开启' if power_status else '关闭'}")
            elif first_byte == 0x53:
                logger.info("  响应类型: 衰减设置响应")
            elif first_byte == 0x54:
                if len(data) >= 3:
                    if data[1] == 0x55 and data[2] == 0x55:
                        logger.info("  响应类型: 功放开启响应")
                    elif data[1] == 0x55 and data[2] == 0xAA:
                        logger.info("  响应类型: 功放关闭响应")
                    else:
                        logger.info("  响应类型: 功放控制响应（未知状态）")
                else:
                    logger.info("  响应类型: 功放控制响应（数据不完整）")
            else:
                logger.info(f"  未识别的响应类型: {hex(first_byte)}")
    except Exception as e:
        logger.error(f"解析响应时发生错误: {e}")

def create_standard_command(cmd_type, *params):
    """
    创建标准命令
    
    Args:
        cmd_type (str): 命令类型，可选值: 'connect', 'query', 'attenuation', 'power_on', 'power_off'
        *params: 可选参数，如衰减值等
        
    Returns:
        str: 十六进制命令字符串
    """
    if cmd_type == 'connect':
        # 连接确认命令: 16 03 A1 55 F4
        data = bytes([0xA1, 0x55])
        crc = calculate_crc(data)
        cmd = bytes([0x16, len(data)]) + data + bytes([crc])
        return hexlify(cmd).decode()
    
    elif cmd_type == 'query':
        # BUC参数查询命令: 16 03 A2 55 F7
        data = bytes([0xA2, 0x55])
        crc = calculate_crc(data)
        cmd = bytes([0x16, len(data)]) + data + bytes([crc])
        return hexlify(cmd).decode()
    
    elif cmd_type == 'attenuation':
        # 衰减设置命令: 16 04 A3 55 XX YY
        if not params or len(params) < 1:
            raise ValueError("衰减设置命令需要指定衰减值参数")
        
        attenuation_value = int(params[0])
        if not 0 <= attenuation_value <= 20:
            raise ValueError("衰减值必须在0~20范围内")
        
        data = bytes([0xA3, 0x55, attenuation_value])
        crc = calculate_crc(data)
        cmd = bytes([0x16, len(data)]) + data + bytes([crc])
        return hexlify(cmd).decode()
    
    elif cmd_type == 'power_on':
        # 功放开启命令: 16 04 A4 55 55 A4
        data = bytes([0xA4, 0x55, 0x55])
        crc = calculate_crc(data)
        cmd = bytes([0x16, len(data)]) + data + bytes([crc])
        return hexlify(cmd).decode()
    
    elif cmd_type == 'power_off':
        # 功放关闭命令: 16 04 A4 55 AA 5B
        data = bytes([0xA4, 0x55, 0xAA])
        crc = calculate_crc(data)
        cmd = bytes([0x16, len(data)]) + data + bytes([crc])
        return hexlify(cmd).decode()
    
    else:
        raise ValueError(f"未知的命令类型: {cmd_type}")

def main():
    """主函数"""
    print("="*50)
    print("BUC功放设备指令集测试工具")
    print("="*50)
    
    # 生成标准命令
    connect_cmd = create_standard_command('connect')
    query_cmd = create_standard_command('query')
    attenuation_cmd = create_standard_command('attenuation', 5)  # 设置衰减值为5
    power_on_cmd = create_standard_command('power_on')
    power_off_cmd = create_standard_command('power_off')
    
    print("\n标准命令:")
    print(f"1. 连接确认命令: {connect_cmd}")
    print(f"2. BUC参数查询命令: {query_cmd}")
    print(f"3. 衰减设置命令 (值=5): {attenuation_cmd}")
    print(f"4. 功放开启命令: {power_on_cmd}")
    print(f"5. 功放关闭命令: {power_off_cmd}")
    
    # 分析用户的命令
    user_command = "1603A255F7"
    print(f"\n分析用户命令: {user_command}")
    analyze_command(user_command)
    
    # 询问用户是否要发送命令
    send_choice = input("\n是否要发送命令? (y/n): ").lower()
    if send_choice == 'y':
        port_name = input("请输入串口名称 (例如COM1): ")
        
        cmd_choice = input("\n请选择要发送的命令类型:\n"
                          "1. 连接确认命令\n"
                          "2. BUC参数查询命令\n"
                          "3. 衰减设置命令\n"
                          "4. 功放开启命令\n"
                          "5. 功放关闭命令\n"
                          "6. 用户原始命令 (1603A255F7)\n"
                          "7. 自定义命令\n"
                          "请输入选择 (1-7): ")
        
        if cmd_choice == '1':
            send_command(port_name, connect_cmd)
        elif cmd_choice == '2':
            send_command(port_name, query_cmd)
        elif cmd_choice == '3':
            atten_value = input("请输入衰减值 (0-20): ")
            try:
                custom_attenuation_cmd = create_standard_command('attenuation', int(atten_value))
                send_command(port_name, custom_attenuation_cmd)
            except ValueError as e:
                print(f"错误: {e}")
        elif cmd_choice == '4':
            send_command(port_name, power_on_cmd)
        elif cmd_choice == '5':
            send_command(port_name, power_off_cmd)
        elif cmd_choice == '6':
            send_command(port_name, user_command)
        elif cmd_choice == '7':
            custom_cmd = input("请输入十六进制命令: ")
            send_command(port_name, custom_cmd)
        else:
            print("无效的选择")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main() 