#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
BUC功放设备测试客户端
用于测试与BUC功放设备模拟器的通信
"""

import argparse
import logging
import serial
import time
from binascii import hexlify
from serial.tools.list_ports import comports

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('BUCClient')

class BUCClient:
    """BUC功放设备测试客户端类"""
    
    def __init__(self, port, baudrate=9600, verbose=False):
        """
        初始化BUC客户端
        
        Args:
            port (str): 串口名称
            baudrate (int): 波特率，默认9600
            verbose (bool): 是否显示详细日志
        """
        self.port_name = port
        self.baudrate = baudrate
        self.port = None
        
        # 日志级别
        if verbose:
            logger.setLevel(logging.DEBUG)
    
    def connect(self):
        """连接到串口"""
        try:
            self.port = serial.Serial(
                port=self.port_name,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=1
            )
            
            logger.info(f"已连接到串口 {self.port_name}，波特率 {self.baudrate}")
            return True
        except serial.SerialException as e:
            logger.error(f"连接串口错误: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        if self.port and self.port.is_open:
            self.port.close()
            logger.info(f"已断开串口 {self.port_name} 的连接")
    
    def send_command(self, data, wait_time=0.5):
        """
        发送命令并等待响应
        
        Args:
            data (bytes): 要发送的数据
            wait_time (float): 等待响应的时间，单位秒
            
        Returns:
            bytes or None: 收到的响应，如果没有响应则为None
        """
        if not self.port or not self.port.is_open:
            logger.error("串口未连接")
            return None
        
        try:
            # 清空接收缓冲区
            self.port.reset_input_buffer()
            
            # 发送命令
            self.port.write(data)
            logger.debug(f"已发送命令: {hexlify(data).decode()}")
            
            # 等待响应
            time.sleep(wait_time)
            
            # 读取响应
            if self.port.in_waiting > 0:
                response = self.port.read(self.port.in_waiting)
                logger.debug(f"收到响应: {hexlify(response).decode()}")
                return response
            else:
                logger.warning("未收到响应")
                return None
        
        except serial.SerialException as e:
            logger.error(f"发送命令错误: {e}")
            return None
    
    def query_device(self):
        """
        设备查询命令
        
        Returns:
            bytes or None: 收到的响应，如果没有响应则为None
        """
        logger.info("发送设备查询命令")
        
        # 构建命令：16 01 31 31
        command = bytes([0x16, 0x01, 0x31, 0x31])
        
        # 发送命令并接收响应
        response = self.send_command(command)
        
        if response:
            logger.info(f"设备查询响应: {hexlify(response).decode()}")
            # 预期响应：11 03 31 AA FB
            if len(response) >= 5 and response[0] == 0x11 and response[1] == 0x03 and response[2] == 0x31:
                logger.info("设备查询成功")
                print("设备查询成功")
            else:
                logger.warning("设备查询响应格式不正确")
                print("设备查询响应格式不正确")
        else:
            print("未收到设备响应")
        
        return response
    
    def query_buc(self):
        """
        BUC单独查询命令
        
        Returns:
            bytes or None: 收到的响应，如果没有响应则为None
        """
        logger.info("发送BUC单独查询命令")
        
        # 构建命令：16 02 32 A2 90
        command = bytes([0x16, 0x02, 0x32, 0xA2, 0x90])
        
        # 发送命令并接收响应
        response = self.send_command(command)
        
        if response:
            logger.info(f"BUC单独查询响应: {hexlify(response).decode()}")
            # 预期响应：11 09 32 55 YY YY YY YY YY YY
            if len(response) >= 10 and response[0] == 0x11 and response[1] == 0x09 and response[2] == 0x32:
                # 解析状态数据
                if len(response) >= 10:
                    status_bytes = response[3:9]
                    temperature = status_bytes[1] / 2.0 if len(status_bytes) > 1 else 0
                    power_level = status_bytes[2] if len(status_bytes) > 2 else 0
                    voltage = status_bytes[3] / 10.0 if len(status_bytes) > 3 else 0
                    current = status_bytes[4] / 10.0 if len(status_bytes) > 4 else 0
                    error_code = status_bytes[5] if len(status_bytes) > 5 else 0
                    
                    status_str = (
                        f"\nBUC状态查询结果:\n"
                        f"温度: {temperature:.1f}°C\n"
                        f"功率: {power_level}%\n"
                        f"电压: {voltage:.1f}V\n"
                        f"电流: {current:.1f}A\n"
                        f"错误码: {error_code}"
                    )
                    print(status_str)
                    
                    logger.info(f"BUC状态: 温度={temperature}°C, 功率={power_level}%, "
                               f"电压={voltage}V, 电流={current}A, 错误码={error_code}")
            else:
                logger.warning("BUC单独查询响应格式不正确")
                print("BUC单独查询响应格式不正确")
        else:
            print("未收到BUC响应")
        
        return response
    
    def set_variable(self, var_id=0xA3, var_value=0x55):
        """
        变量设置命令
        
        Args:
            var_id (int): 变量ID
            var_value (int): 变量值
            
        Returns:
            bytes or None: 收到的响应，如果没有响应则为None
        """
        logger.info(f"发送变量设置命令: ID={hex(var_id)}, 值={hex(var_value)}")
        
        # 构建命令数据部分
        data = bytes([0x33, var_id, var_value])
        
        # 计算CRC
        crc = self._calculate_crc(data)
        
        # 构建完整命令：16 03 33 XX YY CRC
        command = bytes([0x16, 0x03]) + data + bytes([crc])
        
        # 发送命令并接收响应
        response = self.send_command(command)
        
        if response:
            logger.info(f"变量设置响应: {hexlify(response).decode()}")
            # 预期响应：11 04 A3 55 XX YY
            if len(response) >= 6 and response[0] == 0x11 and response[1] == 0x04:
                logger.info("变量设置成功")
                print(f"变量设置成功: ID={hex(var_id)}, 值={hex(var_value)}")
            else:
                logger.warning("变量设置响应格式不正确")
                print("变量设置响应格式不正确")
        else:
            print("未收到变量设置响应")
        
        return response
    
    def power_on(self):
        """
        功放开启命令
        
        Returns:
            bytes or None: 收到的响应，如果没有响应则为None
        """
        logger.info("发送功放开启命令")
        
        # 构建命令：16 02 34 A4 90
        command = bytes([0x16, 0x02, 0x34, 0xA4, 0x90])
        
        # 发送命令并接收响应
        response = self.send_command(command)
        
        if response:
            logger.info(f"功放开启响应: {hexlify(response).decode()}")
            # 预期响应：11 04 54 55 55 54
            if len(response) >= 6 and response[0] == 0x11 and response[1] == 0x04 and response[2] == 0x54:
                logger.info("功放开启成功")
                print("功放已开启")
            else:
                logger.warning("功放开启响应格式不正确")
                print("功放开启响应格式不正确")
        else:
            print("未收到功放开启响应")
        
        return response
    
    def power_off(self):
        """
        功放关闭命令
        
        Returns:
            bytes or None: 收到的响应，如果没有响应则为None
        """
        logger.info("发送功放关闭命令")
        
        # 构建命令：16 02 34 A5 91
        command = bytes([0x16, 0x02, 0x34, 0xA5, 0x91])
        
        # 发送命令并接收响应
        response = self.send_command(command)
        
        if response:
            logger.info(f"功放关闭响应: {hexlify(response).decode()}")
            # 预期响应：11 04 54 AA AB
            if len(response) >= 6 and response[0] == 0x11 and response[1] == 0x04 and response[2] == 0x54:
                logger.info("功放关闭成功")
                print("功放已关闭")
            else:
                logger.warning("功放关闭响应格式不正确")
                print("功放关闭响应格式不正确")
        else:
            print("未收到功放关闭响应")
        
        return response
    
    def run_all_commands(self):
        """执行所有命令进行测试"""
        logger.info("开始执行所有测试命令")
        
        # 设备查询
        self.query_device()
        time.sleep(0.5)
        
        # BUC单独查询
        self.query_buc()
        time.sleep(0.5)
        
        # 变量设置
        self.set_variable()
        time.sleep(0.5)
        
        # 功放开启
        self.power_on()
        time.sleep(0.5)
        
        # BUC单独查询（功放开启后）
        self.query_buc()
        time.sleep(0.5)
        
        # 功放关闭
        self.power_off()
        time.sleep(0.5)
        
        # BUC单独查询（功放关闭后）
        self.query_buc()
        
        logger.info("测试命令执行完毕")
    
    def _calculate_crc(self, data):
        """
        计算CRC校验值（所有数据字节的异或值）
        
        Args:
            data (bytes): 要计算CRC的数据
            
        Returns:
            int: CRC校验值
        """
        crc = 0
        for b in data:
            crc ^= b
        return crc


def list_available_ports():
    """列出可用的串口"""
    available_ports = list(comports())
    if not available_ports:
        print("未检测到可用串口")
        return []
    
    print("可用串口列表:")
    for i, port in enumerate(available_ports):
        print(f"{i+1}. {port.device} - {port.description}")
    
    return [port.device for port in available_ports]


def show_menu():
    """显示交互式菜单"""
    print("\n" + "="*50)
    print("BUC功放设备测试客户端 - 命令菜单")
    print("="*50)
    print("1. 设备查询")
    print("2. BUC单独查询")
    print("3. 变量设置")
    print("4. 功放开启")
    print("5. 功放关闭")
    print("6. 执行所有命令")
    print("0. 退出程序")
    print("="*50)
    
    choice = input("请选择操作 [1-6, 0]: ")
    return choice


def main():
    """主函数"""
    print("="*50)
    print("BUC功放设备测试客户端")
    print("用于测试与BUC功放设备模拟器的通信")
    print("="*50)
    
    # 列出可用串口
    available_ports = list_available_ports()
    
    if not available_ports:
        print("\n请插入串口设备后重试，或手动输入串口名称")
        port = input("\n请输入串口名称（例如COM1）: ")
    else:
        # 用户选择串口或手动输入
        choice = input("\n请选择串口编号，或直接输入串口名称: ")
        
        try:
            # 尝试作为索引处理
            idx = int(choice) - 1
            if 0 <= idx < len(available_ports):
                port = available_ports[idx]
            else:
                print(f"无效的选择，请输入1-{len(available_ports)}之间的数字")
                port = input("请手动输入串口名称: ")
        except ValueError:
            # 用户直接输入了串口名称
            port = choice
    
    # 用户选择波特率
    baudrate_str = input("\n请输入波特率 [9600]: ")
    baudrate = int(baudrate_str) if baudrate_str.strip() else 9600
    
    # 是否显示详细日志
    verbose = input("\n是否显示详细日志? (y/n) [n]: ").lower() == 'y'
    
    print(f"\n正在连接串口: {port}, 波特率: {baudrate}")
    
    # 创建客户端
    client = BUCClient(port, baudrate, verbose)
    
    # 连接串口
    if not client.connect():
        print("串口连接失败，请检查端口名称或确保端口未被其他程序占用")
        return
    
    try:
        # 进入命令循环
        while True:
            choice = show_menu()
            
            if choice == '1':
                client.query_device()
            elif choice == '2':
                client.query_buc()
            elif choice == '3':
                # 获取变量ID和值
                var_id_str = input("请输入变量ID(十六进制，默认A3): 0x")
                var_value_str = input("请输入变量值(十六进制，默认55): 0x")
                
                try:
                    var_id = int(var_id_str, 16) if var_id_str.strip() else 0xA3
                    var_value = int(var_value_str, 16) if var_value_str.strip() else 0x55
                    client.set_variable(var_id, var_value)
                except ValueError:
                    print("输入格式错误，请输入有效的十六进制数")
            elif choice == '4':
                client.power_on()
            elif choice == '5':
                client.power_off()
            elif choice == '6':
                client.run_all_commands()
            elif choice == '0':
                print("正在退出程序...")
                break
            else:
                print("无效的选择，请重新输入")
                
            # 每次操作后暂停
            input("\n按Enter继续...")
    
    finally:
        # 断开连接
        client.disconnect()


if __name__ == '__main__':
    main() 