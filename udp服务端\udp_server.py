#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UDP服务端 - 用于接收UDP数据包
支持多客户端连接，实时显示接收到的数据
"""

import socket
import threading
import time
from datetime import datetime
import argparse
import sys


class UDPServer:
    def __init__(self, host='0.0.0.0', port=28001, buffer_size=1024):
        """
        初始化UDP服务端
        
        Args:
            host (str): 监听地址，默认0.0.0.0（所有接口）
            port (int): 监听端口，默认8888
            buffer_size (int): 接收缓冲区大小，默认1024字节
        """
        self.host = host
        self.port = port
        self.buffer_size = buffer_size
        self.socket = None
        self.running = False
        self.clients = {}  # 存储客户端信息
        self.message_count = 0
        
    def start(self):
        """启动UDP服务端"""
        try:
            # 创建UDP socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            # 绑定地址和端口
            self.socket.bind((self.host, self.port))
            self.running = True
            
            print(f"UDP服务端已启动")
            print(f"监听地址: {self.host}:{self.port}")
            print(f"缓冲区大小: {self.buffer_size} 字节")
            print("等待客户端连接...")
            print("-" * 50)
            
            # 启动接收线程
            receive_thread = threading.Thread(target=self._receive_data, daemon=True)
            receive_thread.start()
            
            # 启动统计线程
            stats_thread = threading.Thread(target=self._print_stats, daemon=True)
            stats_thread.start()
            
            # 主线程等待用户输入
            self._handle_user_input()
            
        except Exception as e:
            print(f"启动UDP服务端失败: {e}")
            self.stop()
    
    def _receive_data(self):
        """接收数据的线程函数"""
        while self.running:
            try:
                # 接收数据和客户端地址
                data, client_addr = self.socket.recvfrom(self.buffer_size)
                
                # 更新客户端信息
                client_key = f"{client_addr[0]}:{client_addr[1]}"
                if client_key not in self.clients:
                    self.clients[client_key] = {
                        'first_seen': datetime.now(),
                        'last_seen': datetime.now(),
                        'message_count': 0
                    }
                    print(f"\n新客户端连接: {client_key}")
                
                self.clients[client_key]['last_seen'] = datetime.now()
                self.clients[client_key]['message_count'] += 1
                self.message_count += 1
                
                # 处理接收到的数据
                self._process_data(data, client_addr)
                
            except socket.error as e:
                if self.running:
                    print(f"接收数据错误: {e}")
            except Exception as e:
                print(f"处理数据时发生错误: {e}")
    
    def _process_data(self, data, client_addr):
        """处理接收到的数据"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        client_info = f"{client_addr[0]}:{client_addr[1]}"

        try:
            # 尝试解码为UTF-8文本
            text_data = data.decode('utf-8')
            print(f"[{timestamp}] 来自 {client_info} | {text_data}")
        except UnicodeDecodeError:
            # 尝试ASCII解码，过滤控制字符
            try:
                ascii_data = data.decode('ascii', errors='ignore')
                # 过滤掉控制字符，只保留可打印字符
                clean_data = ''.join(c if c.isprintable() else ' ' for c in ascii_data)
                # 压缩多个空格为单个空格
                clean_data = ' '.join(clean_data.split())
                print(f"[{timestamp}] 来自 {client_info} | {clean_data}")
            except:
                # 如果都不行，显示十六进制
                hex_data = data.hex()
                print(f"[{timestamp}] 来自 {client_info} | {hex_data}")


    def _print_stats(self):
        """定期打印统计信息"""
        while self.running:
            time.sleep(30)  # 每30秒打印一次统计
            if self.clients:
                print(f"\n=== 统计信息 ===")
                print(f"总消息数: {self.message_count}")
                print(f"活跃客户端数: {len(self.clients)}")
                for client, info in self.clients.items():
                    last_seen = info['last_seen'].strftime("%H:%M:%S")
                    print(f"  {client}: {info['message_count']} 条消息, 最后活跃: {last_seen}")
                print("=" * 20)
    
    def _handle_user_input(self):
        """处理用户输入命令"""
        print("\n可用命令:")
        print("  stats - 显示统计信息")
        print("  clients - 显示客户端列表")
        print("  clear - 清除统计信息")
        print("  quit/exit - 退出服务端")
        print()
        
        while self.running:
            try:
                command = input().strip().lower()
                
                if command in ['quit', 'exit', 'q']:
                    break
                elif command == 'stats':
                    self._show_stats()
                elif command == 'clients':
                    self._show_clients()
                elif command == 'clear':
                    self._clear_stats()
                elif command == 'help':
                    print("可用命令: stats, clients, clear, quit/exit")
                elif command:
                    print(f"未知命令: {command}，输入 help 查看可用命令")
                    
            except KeyboardInterrupt:
                break
            except EOFError:
                break
        
        self.stop()
    
    def _show_stats(self):
        """显示统计信息"""
        print(f"\n=== 详细统计信息 ===")
        print(f"服务端地址: {self.host}:{self.port}")
        print(f"总消息数: {self.message_count}")
        print(f"客户端数量: {len(self.clients)}")
        print(f"运行状态: {'运行中' if self.running else '已停止'}")
        
        if self.clients:
            print("\n客户端详情:")
            for client, info in self.clients.items():
                first_seen = info['first_seen'].strftime("%Y-%m-%d %H:%M:%S")
                last_seen = info['last_seen'].strftime("%Y-%m-%d %H:%M:%S")
                print(f"  {client}:")
                print(f"    首次连接: {first_seen}")
                print(f"    最后活跃: {last_seen}")
                print(f"    消息数量: {info['message_count']}")
        print("=" * 25)
    
    def _show_clients(self):
        """显示客户端列表"""
        if not self.clients:
            print("当前没有客户端连接")
            return
            
        print(f"\n=== 客户端列表 ({len(self.clients)}) ===")
        for client, info in self.clients.items():
            last_seen = info['last_seen'].strftime("%H:%M:%S")
            print(f"  {client} - {info['message_count']} 条消息, 最后活跃: {last_seen}")
        print("=" * 30)
    
    def _clear_stats(self):
        """清除统计信息"""
        self.clients.clear()
        self.message_count = 0
        print("统计信息已清除")
    
    def stop(self):
        """停止UDP服务端"""
        print("\n正在停止UDP服务端...")
        self.running = False
        
        if self.socket:
            self.socket.close()
        
        print("UDP服务端已停止")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='UDP服务端 - 接收UDP数据包')
    parser.add_argument('--host', default='**************', help='监听地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=28001, help='监听端口 (默认: 28001)')
    parser.add_argument('--buffer', type=int, default=1024, help='接收缓冲区大小 (默认: 1024)')
    
    args = parser.parse_args()
    
    # 创建并启动UDP服务端
    server = UDPServer(host=args.host, port=args.port, buffer_size=args.buffer)
    
    try:
        server.start()
    except KeyboardInterrupt:
        print("\n收到中断信号")
    finally:
        server.stop()


if __name__ == "__main__":
    main()
