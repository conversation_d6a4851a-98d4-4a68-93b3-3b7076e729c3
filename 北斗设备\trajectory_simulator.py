#!/usr/bin/env python
# -*- coding: utf-8 -*-

import math
import time
import json
import os
from datetime import datetime, timedelta

class TrajectorySimulator:
    """轨迹模拟器，用于生成不同类型的轨迹"""
    
    # 地球半径（米）
    EARTH_RADIUS = 6371000
    
    def __init__(self, start_lat=39.9042, start_lon=116.4074, speed=5.0):
        """初始化轨迹模拟器
        
        Args:
            start_lat: 起始纬度，默认为北京天安门（39.9042°N）
            start_lon: 起始经度，默认为北京天安门（116.4074°E）
            speed: 模拟速度，单位节（1节 = 0.5144米/秒）
        """
        self.current_lat = start_lat
        self.current_lon = start_lon
        self.speed = speed  # 速度，单位节
        self.speed_mps = speed * 0.5144  # 转换为米/秒
        self.course = 0.0  # 航向，单位度
        self.start_time = datetime.utcnow()
        self.current_time = self.start_time
        
    def _update_position(self, lat, lon, course):
        """更新当前位置和航向
        
        Args:
            lat: 新的纬度
            lon: 新的经度
            course: 新的航向
        """
        self.current_lat = lat
        self.current_lon = lon
        self.course = course
        self.current_time = datetime.utcnow()
        
    def _calculate_distance(self, lat1, lon1, lat2, lon2):
        """计算两点之间的距离（米）
        
        Args:
            lat1: 第一点纬度（度）
            lon1: 第一点经度（度）
            lat2: 第二点纬度（度）
            lon2: 第二点经度（度）
            
        Returns:
            两点之间的距离（米）
        """
        # 转换为弧度
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)
        
        # 半正矢公式
        dlon = lon2_rad - lon1_rad
        dlat = lat2_rad - lat1_rad
        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = self.EARTH_RADIUS * c
        
        return distance
    
    def _calculate_bearing(self, lat1, lon1, lat2, lon2):
        """计算两点之间的方位角（度）
        
        Args:
            lat1: 第一点纬度（度）
            lon1: 第一点经度（度）
            lat2: 第二点纬度（度）
            lon2: 第二点经度（度）
            
        Returns:
            从第一点到第二点的方位角（度，0-360）
        """
        # 转换为弧度
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)
        
        # 计算方位角
        dlon = lon2_rad - lon1_rad
        y = math.sin(dlon) * math.cos(lat2_rad)
        x = math.cos(lat1_rad) * math.sin(lat2_rad) - math.sin(lat1_rad) * math.cos(lat2_rad) * math.cos(dlon)
        bearing_rad = math.atan2(y, x)
        
        # 转换为度，并确保在0-360范围内
        bearing = math.degrees(bearing_rad)
        bearing = (bearing + 360) % 360
        
        return bearing
    
    def _calculate_new_position(self, lat, lon, bearing, distance):
        """根据起点、方位角和距离计算终点坐标
        
        Args:
            lat: 起点纬度（度）
            lon: 起点经度（度）
            bearing: 方位角（度）
            distance: 距离（米）
            
        Returns:
            tuple: (终点纬度, 终点经度)
        """
        # 转换为弧度
        lat_rad = math.radians(lat)
        lon_rad = math.radians(lon)
        bearing_rad = math.radians(bearing)
        
        # 计算角距离
        angular_distance = distance / self.EARTH_RADIUS
        
        # 计算终点坐标
        lat2_rad = math.asin(
            math.sin(lat_rad) * math.cos(angular_distance) + 
            math.cos(lat_rad) * math.sin(angular_distance) * math.cos(bearing_rad)
        )
        
        lon2_rad = lon_rad + math.atan2(
            math.sin(bearing_rad) * math.sin(angular_distance) * math.cos(lat_rad),
            math.cos(angular_distance) - math.sin(lat_rad) * math.sin(lat2_rad)
        )
        
        # 转换为度
        lat2 = math.degrees(lat2_rad)
        lon2 = math.degrees(lon2_rad)
        
        return lat2, lon2
    
    def move_line(self, bearing, time_interval=1.0):
        """沿直线移动
        
        Args:
            bearing: 方位角（度，0-360）
            time_interval: 时间间隔（秒）
            
        Returns:
            tuple: (当前纬度, 当前经度, 当前航向, 当前时间)
        """
        # 计算在给定时间内移动的距离（米）
        distance = self.speed_mps * time_interval
        
        # 计算新位置
        new_lat, new_lon = self._calculate_new_position(
            self.current_lat, self.current_lon, bearing, distance
        )
        
        # 更新位置和航向
        self._update_position(new_lat, new_lon, bearing)
        
        return self.current_lat, self.current_lon, self.course, self.current_time
    
    def move_circle(self, center_lat, center_lon, radius, clockwise=True, time_interval=1.0):
        """沿圆形轨迹移动
        
        Args:
            center_lat: 圆心纬度
            center_lon: 圆心经度
            radius: 半径（米）
            clockwise: 是否顺时针
            time_interval: 时间间隔（秒）
            
        Returns:
            tuple: (当前纬度, 当前经度, 当前航向, 当前时间)
        """
        # 计算当前点到圆心的方位角
        current_bearing = self._calculate_bearing(center_lat, center_lon, self.current_lat, self.current_lon)
        
        # 计算在给定时间内沿圆周移动的角度（弧度）
        # 圆周长 = 2 * pi * radius
        # 速度 = self.speed_mps
        # 时间 = time_interval
        # 角速度 = 速度 / 半径
        # 角度变化 = 角速度 * 时间
        angular_speed = self.speed_mps / radius
        angle_change = angular_speed * time_interval
        
        # 转换为度
        angle_change_deg = math.degrees(angle_change)
        
        # 计算新的方位角（顺时针或逆时针）
        if clockwise:
            new_bearing = (current_bearing + angle_change_deg) % 360
        else:
            new_bearing = (current_bearing - angle_change_deg) % 360
        
        # 计算新位置
        new_lat, new_lon = self._calculate_new_position(
            center_lat, center_lon, new_bearing, radius
        )
        
        # 计算航向（切线方向，与半径方向垂直）
        if clockwise:
            course = (new_bearing + 90) % 360
        else:
            course = (new_bearing - 90) % 360
        
        # 更新位置和航向
        self._update_position(new_lat, new_lon, course)
        
        return self.current_lat, self.current_lon, self.course, self.current_time
    
    def load_trajectory_from_file(self, file_path):
        """从文件加载轨迹
        
        文件格式：JSON数组，每个元素包含lat（纬度）和lon（经度）
        
        Args:
            file_path: 轨迹文件路径
            
        Returns:
            轨迹点列表，每个点为(纬度, 经度)元组
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"轨迹文件不存在: {file_path}")
        
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        trajectory = []
        for point in data:
            if 'lat' in point and 'lon' in point:
                trajectory.append((point['lat'], point['lon']))
        
        return trajectory
    
    def move_along_trajectory(self, trajectory, current_index=0, time_interval=1.0):
        """沿预定义轨迹移动
        
        Args:
            trajectory: 轨迹点列表，每个点为(纬度, 经度)元组
            current_index: 当前轨迹点索引
            time_interval: 时间间隔（秒）
            
        Returns:
            tuple: (当前纬度, 当前经度, 当前航向, 当前时间, 下一个轨迹点索引)
        """
        if not trajectory or len(trajectory) < 2:
            raise ValueError("轨迹至少需要包含两个点")
        
        # 确保索引在有效范围内
        current_index = current_index % len(trajectory)
        next_index = (current_index + 1) % len(trajectory)
        
        # 获取当前点和下一个点
        current_point = trajectory[current_index]
        next_point = trajectory[next_index]
        
        # 计算两点之间的距离和方位角
        distance = self._calculate_distance(
            current_point[0], current_point[1], next_point[0], next_point[1]
        )
        bearing = self._calculate_bearing(
            current_point[0], current_point[1], next_point[0], next_point[1]
        )
        
        # 计算在给定时间内移动的距离
        move_distance = self.speed_mps * time_interval
        
        # 如果移动距离大于等于两点之间的距离，则移动到下一个点
        if move_distance >= distance:
            self._update_position(next_point[0], next_point[1], bearing)
            return next_point[0], next_point[1], bearing, self.current_time, next_index
        
        # 否则，沿着两点连线移动
        new_lat, new_lon = self._calculate_new_position(
            current_point[0], current_point[1], bearing, move_distance
        )
        
        # 更新位置和航向
        self._update_position(new_lat, new_lon, bearing)
        
        return new_lat, new_lon, bearing, self.current_time, current_index 