#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据转换演示 - 展示如何将您的十六进制数据转换为可读格式
"""

import re
from datetime import datetime

def convert_hex_to_readable(hex_string):
    """将十六进制数据转换为可读格式"""
    print("=" * 60)
    print("数据转换演示")
    print("=" * 60)
    
    print(f"原始十六进制数据:")
    print(f"{hex_string}")
    print(f"数据长度: {len(hex_string)} 字符 ({len(hex_string)//2} 字节)")
    print()
    
    try:
        # 转换为字节
        data_bytes = bytes.fromhex(hex_string)
        
        # 解码为ASCII文本（忽略错误）
        text_data = data_bytes.decode('ascii', errors='ignore')
        print(f"解码后的文本内容:")
        print(f"'{text_data}'")
        print()
        
        # 分析数据结构
        print("数据结构分析:")
        print("-" * 40)
        
        # 查找设备信息
        if ',' in text_data:
            first_part = text_data.split(',')[0]
            if first_part.strip():
                print(f"设备标识: {first_part}")
        
        # 查找日期时间
        datetime_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})'
        datetime_match = re.search(datetime_pattern, text_data)
        if datetime_match:
            gps_time = datetime_match.group(1)
            print(f"GPS时间: {gps_time}")
        
        # 查找坐标信息
        coord_pattern = r'(\d+\.\d+),([NS]),(\d+\.\d+),([EW])'
        coord_match = re.search(coord_pattern, text_data)
        if coord_match:
            lat_value = float(coord_match.group(1))
            lat_dir = coord_match.group(2)
            lon_value = float(coord_match.group(3))
            lon_dir = coord_match.group(4)
            
            # 转换为十进制度数
            lat_decimal = convert_to_decimal_degrees(lat_value)
            if lat_dir == 'S':
                lat_decimal = -lat_decimal
            
            lon_decimal = convert_to_decimal_degrees(lon_value)
            if lon_dir == 'W':
                lon_decimal = -lon_decimal
            
            print(f"纬度: {lat_value}° {lat_dir} = {lat_decimal:.6f}°")
            print(f"经度: {lon_value}° {lon_dir} = {lon_decimal:.6f}°")
            print()
            print(f"地图位置:")
            print(f"  Google Maps: https://maps.google.com/?q={lat_decimal},{lon_decimal}")
            print(f"  百度地图: https://map.baidu.com/?q={lat_decimal},{lon_decimal}")
        
        print()
        print("格式化显示:")
        print("-" * 40)
        
        # 模拟UDP服务端的显示格式
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        client_info = "192.168.1.100:12345"
        
        print(f"[{timestamp}] 来自 {client_info} | 二进制数据 ({len(data_bytes)} 字节): {hex_string}")
        
        if datetime_match and coord_match:
            print(f"[{timestamp}] 来自 {client_info} | GPS数据:")
            print(f"    时间: {gps_time}")
            print(f"    位置: {lat_decimal:.6f}°{lat_dir}, {lon_decimal:.6f}°{lon_dir}")
            
            if ',' in text_data:
                device_info = text_data.split(',')[0]
                if device_info.strip():
                    print(f"    设备: {device_info}")
        
    except Exception as e:
        print(f"转换错误: {e}")

def convert_to_decimal_degrees(coord_value):
    """将度分格式转换为十进制度数"""
    # 格式: DDMM.MMMM (度分.分的小数部分)
    degrees = int(coord_value // 100)
    minutes = coord_value % 100
    decimal_degrees = degrees + minutes / 60.0
    return decimal_degrees

def main():
    """主函数"""
    # 您的GPS数据
    sample_data = "4263316366722c3432323331363300685cfb0e00000197ab34af3d002030333242323032352d30362d32362031363a30323a34332c32312e32313138333938362c4e2c3131342e34343233353731392c45"
    
    convert_hex_to_readable(sample_data)
    
    print("\n" + "=" * 60)
    print("转换完成！")
    print("现在您的UDP服务端可以自动识别并格式化显示这种GPS数据了。")

if __name__ == "__main__":
    main()
