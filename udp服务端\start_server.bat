@echo off
chcp 65001 >nul
echo ========================================
echo           UDP服务端启动脚本
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo Python版本:
python --version
echo.

REM 检查脚本文件是否存在
if not exist "udp_server.py" (
    echo 错误: 未找到 udp_server.py 文件
    pause
    exit /b 1
)

echo 启动UDP服务端...
echo 默认监听地址: 0.0.0.0:8888
echo 按 Ctrl+C 停止服务端
echo.
echo ========================================
echo.

REM 启动UDP服务端
python udp_server.py

echo.
echo UDP服务端已停止
pause
