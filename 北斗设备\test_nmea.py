#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试NMEA数据生成功能，不需要实际串口连接
"""

import time
from datetime import datetime
from nmea_generator import NMEAGenerator
from trajectory_simulator import TrajectorySimulator

def test_line_trajectory():
    """测试直线轨迹NMEA数据生成"""
    print("=== 测试直线轨迹NMEA数据生成 ===")
    
    # 创建轨迹模拟器，起始位置为北京天安门
    simulator = TrajectorySimulator(start_lat=39.9042, start_lon=116.4074, speed=5.0)
    
    # 向东北方向移动（方位角45度）
    bearing = 45.0
    
    # 生成10个点
    for i in range(10):
        # 模拟1秒移动
        lat, lon, course, timestamp = simulator.move_line(bearing, 1.0)
        
        # 生成北斗RMC数据
        bd_rmc = NMEAGenerator.generate_rmc(lat, lon, simulator.speed, course, timestamp, "BD")
        
        # 生成GPS RMC数据
        gn_rmc = NMEAGenerator.generate_rmc(lat, lon, simulator.speed, course, timestamp, "GN")
        
        print(f"点 {i+1}:")
        print(f"  位置: 纬度 {lat:.6f}°, 经度 {lon:.6f}°, 航向 {course:.2f}°")
        print(f"  北斗RMC: {bd_rmc}")
        print(f"  GPS RMC: {gn_rmc}")
        print()
        
        # 暂停0.5秒
        time.sleep(0.5)

def test_circle_trajectory():
    """测试圆形轨迹NMEA数据生成"""
    print("=== 测试圆形轨迹NMEA数据生成 ===")
    
    # 创建轨迹模拟器，起始位置为北京天安门
    simulator = TrajectorySimulator(start_lat=39.9042, start_lon=116.4074, speed=5.0)
    
    # 设置圆心（当前位置）
    center_lat = simulator.current_lat
    center_lon = simulator.current_lon
    
    # 设置半径（100米）
    radius = 100
    
    # 顺时针方向
    clockwise = True
    
    # 生成10个点
    for i in range(10):
        # 模拟1秒移动
        lat, lon, course, timestamp = simulator.move_circle(center_lat, center_lon, radius, clockwise, 1.0)
        
        # 生成北斗RMC数据
        bd_rmc = NMEAGenerator.generate_rmc(lat, lon, simulator.speed, course, timestamp, "BD")
        
        print(f"点 {i+1}:")
        print(f"  位置: 纬度 {lat:.6f}°, 经度 {lon:.6f}°, 航向 {course:.2f}°")
        print(f"  北斗RMC: {bd_rmc}")
        print()
        
        # 暂停0.5秒
        time.sleep(0.5)

def test_file_trajectory():
    """测试文件轨迹NMEA数据生成"""
    print("=== 测试文件轨迹NMEA数据生成 ===")
    
    # 创建轨迹模拟器
    simulator = TrajectorySimulator(speed=5.0)
    
    try:
        # 加载轨迹文件
        trajectory = simulator.load_trajectory_from_file("trajectory.json")
        
        # 初始索引
        current_index = 0
        
        # 生成轨迹点数据
        for i in range(len(trajectory)):
            # 模拟1秒移动
            lat, lon, course, timestamp, current_index = simulator.move_along_trajectory(
                trajectory, current_index, 1.0
            )
            
            # 生成北斗RMC数据
            bd_rmc = NMEAGenerator.generate_rmc(lat, lon, simulator.speed, course, timestamp, "BD")
            
            print(f"点 {i+1}:")
            print(f"  位置: 纬度 {lat:.6f}°, 经度 {lon:.6f}°, 航向 {course:.2f}°")
            print(f"  北斗RMC: {bd_rmc}")
            print()
            
            # 暂停0.5秒
            time.sleep(0.5)
            
    except Exception as e:
        print(f"测试文件轨迹失败: {str(e)}")

def main():
    """主函数"""
    print("NMEA数据生成测试")
    print("=" * 40)
    
    # 测试直线轨迹
    test_line_trajectory()
    
    print("\n" + "=" * 40 + "\n")
    
    # 测试圆形轨迹
    test_circle_trajectory()
    
    print("\n" + "=" * 40 + "\n")
    
    # 测试文件轨迹
    test_file_trajectory()

if __name__ == "__main__":
    main() 