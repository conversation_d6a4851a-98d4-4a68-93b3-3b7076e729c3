#!/usr/bin/env python
# -*- coding: utf-8 -*-

import serial
import time
import random
import re
import argparse
import math
import numpy as np
from datetime import datetime, timedelta
import threading

class WeatherInstrumentSimulator:
    def __init__(self, port, baudrate=9600, timeout=1):
        """Initialize the weather instrument simulator."""
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.serial_port = None
        self.running = False
        
        # 初始化随机种子
        random.seed()
        
        # 初始气象参数 - 可以根据命令行参数自定义
        self.wind_direction = random.randint(0, 359)  # 风向角度
        self.wind_speed = random.uniform(1.5, 2.5)    # 风速 m/s
        self.temperature = random.uniform(15.0, 25.0) # 温度 °C
        self.humidity = random.uniform(60.0, 80.0)    # 湿度 %
        self.pressure = random.uniform(1010.0, 1020.0) # 气压 hPa
        
        # 变化范围和趋势参数
        self.wind_dir_variation = 15      # 风向最大变化幅度
        self.wind_speed_variation = 0.5   # 风速最大变化幅度
        self.temp_variation = 0.2         # 温度最大变化幅度
        self.humidity_variation = 1.0     # 湿度最大变化幅度
        self.pressure_variation = 0.3     # 气压最大变化幅度
        
        # 趋势控制参数
        self.trend_duration = random.randint(60, 180)  # 趋势持续时间(秒)
        self.trend_counter = 0
        self.wind_dir_trend = 0           # 风向趋势
        self.wind_speed_trend = 0         # 风速趋势
        self.temp_trend = 0               # 温度趋势
        self.humidity_trend = 0           # 湿度趋势
        self.pressure_trend = 0           # 气压趋势
        
        # 初始化趋势
        self._update_trends()
        
        # 噪声参数（用于模拟小幅波动）
        self.noise_factor = 0.3
        
        # 记录上一次的值，用于平滑变化
        self.last_wind_dir = self.wind_direction
        self.last_wind_speed = self.wind_speed
        
        # 设置WIXDR发送间隔的随机性
        self.wixdr_interval = random.randint(10, 14)  # 10-14秒发送一次WIXDR
        
        # 模拟信号质量
        self.signal_quality = 0.98  # 98%的信号质量（偶尔会有数据丢失或延迟）

    def connect(self):
        """Connect to the serial port."""
        try:
            self.serial_port = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout
            )
            print(f"Connected to {self.port}")
            return True
        except Exception as e:
            print(f"Error connecting to {self.port}: {e}")
            return False

    def disconnect(self):
        """Disconnect from the serial port."""
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
            print(f"Disconnected from {self.port}")

    def calculate_nmea_checksum(self, sentence):
        """Calculate the NMEA checksum for a sentence."""
        checksum = 0
        for char in sentence[1:]:  # Skip the $ at the beginning
            checksum ^= ord(char)
        return f"{checksum:02X}"  # Return as a 2-digit hexadecimal

    def _update_trends(self):
        """更新各参数的趋势"""
        # 风向趋势：顺时针或逆时针
        self.wind_dir_trend = random.choice([-1, 1]) * random.uniform(0.2, 1.0)
        
        # 风速趋势：增加或减少
        self.wind_speed_trend = random.choice([-1, 1]) * random.uniform(0.01, 0.05)
        
        # 温度趋势：增加或减少
        self.temp_trend = random.choice([-1, 1]) * random.uniform(0.005, 0.02)
        
        # 湿度趋势：与温度趋势相反的概率更大
        if random.random() < 0.7:  # 70%概率与温度趋势相反
            self.humidity_trend = -1 * np.sign(self.temp_trend) * random.uniform(0.01, 0.05)
        else:
            self.humidity_trend = random.choice([-1, 1]) * random.uniform(0.01, 0.05)
        
        # 气压趋势：缓慢变化
        self.pressure_trend = random.choice([-1, 1]) * random.uniform(0.001, 0.01)

    def _apply_brownian_motion(self, value, variation, trend, min_val, max_val):
        """应用布朗运动模型，使数据变化更自然"""
        # 趋势影响
        trend_component = trend
        
        # 随机波动（布朗运动）
        noise = random.normalvariate(0, 1) * variation * self.noise_factor
        
        # 组合趋势和噪声
        new_value = value + trend_component + noise
        
        # 确保在合理范围内
        return max(min_val, min(max_val, new_value))

    def _simulate_data_loss(self):
        """模拟数据丢失或延迟"""
        if random.random() > self.signal_quality:
            delay = random.uniform(0.1, 0.5)  # 随机延迟0.1-0.5秒
            time.sleep(delay)
            return True  # 表示发生了延迟
        return False  # 表示正常

    def generate_wimwv_sentence(self):
        """Generate a WIMWV (wind) NMEA sentence."""
        # 应用趋势和随机变化到风向
        self.wind_direction = self._apply_brownian_motion(
            self.wind_direction, 
            self.wind_dir_variation, 
            self.wind_dir_trend,
            0, 359.9
        )
        # 确保风向在0-360度范围内
        self.wind_direction = self.wind_direction % 360
        
        # 平滑风向变化（避免突变）
        smoothed_wind_dir = 0.7 * self.last_wind_dir + 0.3 * self.wind_direction
        self.last_wind_dir = smoothed_wind_dir
        
        # 应用趋势和随机变化到风速
        self.wind_speed = self._apply_brownian_motion(
            self.wind_speed, 
            self.wind_speed_variation, 
            self.wind_speed_trend,
            0.5, 10.0  # 风速范围更广
        )
        
        # 平滑风速变化
        smoothed_wind_speed = 0.8 * self.last_wind_speed + 0.2 * self.wind_speed
        self.last_wind_speed = smoothed_wind_speed
        
        # 生成NMEA语句
        # 有时使用整数风向，有时使用小数点后一位，模拟不同设备特性
        if random.random() < 0.8:  # 80%概率使用整数
            wind_dir_str = f"{int(round(smoothed_wind_dir))}"
        else:
            wind_dir_str = f"{smoothed_wind_dir:.1f}"
            
        # 风速总是保留一位小数
        wind_speed_str = f"{smoothed_wind_speed:.1f}"
        
        sentence = f"$WIMWV,{wind_dir_str},R,{wind_speed_str},M,A"
        checksum = self.calculate_nmea_checksum(sentence)
        return f"{sentence}*{checksum}"

    def generate_wixdr_sentence(self):
        """Generate a WIXDR (transducer) NMEA sentence for temperature, humidity, and pressure."""
        # 应用趋势和随机变化到温度
        self.temperature = self._apply_brownian_motion(
            self.temperature, 
            self.temp_variation, 
            self.temp_trend,
            -20.0, 50.0
        )
        
        # 应用趋势和随机变化到湿度
        self.humidity = self._apply_brownian_motion(
            self.humidity, 
            self.humidity_variation, 
            self.humidity_trend,
            10.0, 100.0
        )
        
        # 应用趋势和随机变化到气压
        self.pressure = self._apply_brownian_motion(
            self.pressure, 
            self.pressure_variation, 
            self.pressure_trend,
            950.0, 1050.0
        )
        
        # 生成NMEA语句
        sentence = f"$WIXDR,C,{self.temperature:.1f},C,0,H,{self.humidity:.1f},P,0,P,{self.pressure:.1f},H,0"
        checksum = self.calculate_nmea_checksum(sentence)
        return f"{sentence}*{checksum}"

    def send_data(self, data):
        """Send data to the serial port."""
        if self.serial_port and self.serial_port.is_open:
            try:
                formatted_data = f"{data}\r\n"
                self.serial_port.write(formatted_data.encode())
                current_time = datetime.now().strftime("[%H:%M:%S.%f")[:-3] + "]"
                print(f"{current_time}发→{formatted_data.strip()}")
                return True
            except Exception as e:
                print(f"Error sending data: {e}")
                return False
        else:
            print("Serial port is not open")
            return False

    def start_simulation(self):
        """Start the simulation."""
        if not self.serial_port or not self.serial_port.is_open:
            if not self.connect():
                return False
        
        self.running = True
        print("Starting weather instrument simulation...")
        print(f"Initial conditions: Wind {int(self.wind_direction)}° at {self.wind_speed:.1f} m/s, " +
              f"Temp {self.temperature:.1f}°C, Humidity {self.humidity:.1f}%, " +
              f"Pressure {self.pressure:.1f} hPa")
        
        # 创建独立线程运行模拟
        self.simulation_thread = threading.Thread(target=self._run_simulation)
        self.simulation_thread.daemon = True
        self.simulation_thread.start()
        return True

    def _run_simulation(self):
        """Run the simulation in a separate thread."""
        wixdr_counter = 0
        
        while self.running:
            try:
                # 更新趋势计数器
                self.trend_counter += 1
                if self.trend_counter >= self.trend_duration:
                    self._update_trends()
                    self.trend_counter = 0
                    self.trend_duration = random.randint(60, 180)  # 重新设置趋势持续时间
                
                # 模拟数据丢失或延迟
                data_delayed = self._simulate_data_loss()
                
                # 发送WIMWV语句（每秒一次）
                if not data_delayed:  # 如果没有延迟，正常发送
                    self.send_data(self.generate_wimwv_sentence())
                
                # 发送WIXDR语句（约每12秒一次，有随机变化）
                wixdr_counter += 1
                if wixdr_counter >= self.wixdr_interval:
                    # 有时WIXDR会稍微延迟一点发送
                    time.sleep(random.uniform(0.1, 0.3))
                    self.send_data(self.generate_wixdr_sentence())
                    wixdr_counter = 0
                    # 重新设置下一次WIXDR的发送间隔
                    self.wixdr_interval = random.randint(10, 14)
                
                # 等待约1秒，加入少量随机变化模拟真实设备的不精确计时
                time.sleep(random.uniform(0.95, 1.05))
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Error in simulation: {e}")
                time.sleep(1)  # 出错后等待1秒再继续

    def stop_simulation(self):
        """Stop the simulation."""
        self.running = False
        if hasattr(self, 'simulation_thread'):
            self.simulation_thread.join(timeout=2)
        self.disconnect()
        print("Simulation stopped")


def list_com_ports():
    """List available COM ports."""
    ports = []
    try:
        from serial.tools.list_ports import comports
        ports = list(comports())
        if ports:
            print("Available COM ports:")
            for i, port in enumerate(ports):
                print(f"{i+1}. {port.device} - {port.description}")
        else:
            print("No COM ports found")
    except Exception as e:
        print(f"Error listing COM ports: {e}")
    
    return [port.device for port in ports]


def main():
    """Main function to run the simulator."""
    parser = argparse.ArgumentParser(description='Weather Instrument Simulator')
    parser.add_argument('--port', type=str, help='Serial port to use (e.g., COM3)')
    parser.add_argument('--baudrate', type=int, default=9600, help='Baudrate for serial communication')
    parser.add_argument('--temp', type=float, help='Initial temperature in Celsius')
    parser.add_argument('--humidity', type=float, help='Initial humidity percentage')
    parser.add_argument('--pressure', type=float, help='Initial pressure in hPa')
    parser.add_argument('--wind-dir', type=float, help='Initial wind direction in degrees')
    parser.add_argument('--wind-speed', type=float, help='Initial wind speed in m/s')
    args = parser.parse_args()
    
    port = args.port
    
    # If no port is specified, list available ports and ask user to select one
    if not port:
        available_ports = list_com_ports()
        if not available_ports:
            print("No COM ports found. Please specify a port with --port.")
            return
        
        if len(available_ports) == 1:
            port = available_ports[0]
            print(f"Automatically selected the only available port: {port}")
        else:
            try:
                selection = int(input("Enter the number of the port to use: "))
                if 1 <= selection <= len(available_ports):
                    port = available_ports[selection-1]
                else:
                    print("Invalid selection")
                    return
            except ValueError:
                print("Invalid input")
                return
    
    simulator = WeatherInstrumentSimulator(port=port, baudrate=args.baudrate)
    
    # 设置初始参数（如果提供）
    if args.temp is not None:
        simulator.temperature = args.temp
    if args.humidity is not None:
        simulator.humidity = args.humidity
    if args.pressure is not None:
        simulator.pressure = args.pressure
    if args.wind_dir is not None:
        simulator.wind_direction = args.wind_dir
        simulator.last_wind_dir = args.wind_dir
    if args.wind_speed is not None:
        simulator.wind_speed = args.wind_speed
        simulator.last_wind_speed = args.wind_speed
    
    try:
        if simulator.start_simulation():
            print("Press Ctrl+C to stop the simulation")
            # Keep the main thread alive
            while simulator.running:
                time.sleep(1)
        else:
            print("Failed to start simulation")
    except KeyboardInterrupt:
        print("\nStopping simulation...")
    finally:
        simulator.stop_simulation()


if __name__ == "__main__":
    main() 